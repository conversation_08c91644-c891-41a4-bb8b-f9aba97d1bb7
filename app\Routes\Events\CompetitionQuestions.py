from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Events.CompetitionQuestions import (
    create_competition_question, get_competition_questions, update_competition_question,
    delete_competition_question, reorder_competition_questions, generate_ai_questions,
    CompetitionQuestionCreate, CompetitionQuestionOut
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


@router.post("/competitions/{competition_id}/questions")
def create_question(
    competition_id: UUID,
    question_text: str,
    question_type: str,
    difficulty: str = "medium",
    marks: int = 1,
    image_url: str = None,
    code_snippet: str = None,
    expected_answer: str = None,
    options: List[str] = None,
    correct_option: int = None,
    subject: str = None,
    topic: str = None,
    tags: List[str] = None,
    estimated_time_minutes: int = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create a new competition question"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can create questions")
    
    question_data = CompetitionQuestionCreate(
        question_text=question_text,
        question_type=question_type,
        difficulty=difficulty,
        marks=marks,
        image_url=image_url,
        code_snippet=code_snippet,
        expected_answer=expected_answer,
        options=options,
        correct_option=correct_option,
        subject=subject,
        topic=topic,
        tags=tags,
        estimated_time_minutes=estimated_time_minutes
    )
    
    return create_competition_question(db, competition_id, question_data, current_user.id)


@router.get("/competitions/{competition_id}/questions")
def get_questions(
    competition_id: UUID,
    include_answers: bool = Query(False, description="Include answer details"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get questions for a competition"""
    current_user = get_current_user(token, db)
    return {
        "questions": get_competition_questions(db, competition_id, current_user.id, include_answers)
    }


@router.get("/competitions/{competition_id}/questions/public")
def get_public_questions(
    competition_id: UUID,
    db: Session = Depends(get_db)
):
    """Get public questions for a competition (no authentication required)"""
    return {
        "questions": get_competition_questions(db, competition_id)
    }


@router.put("/competitions/questions/{question_id}")
def update_question(
    question_id: UUID,
    question_data: Dict[str, Any],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Update a competition question"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can update questions")
    
    return update_competition_question(db, question_id, question_data, current_user.id)


@router.delete("/competitions/questions/{question_id}")
def delete_question(
    question_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Delete a competition question"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can delete questions")
    
    delete_competition_question(db, question_id, current_user.id)
    return {"message": "Question deleted successfully"}


@router.post("/competitions/{competition_id}/questions/reorder")
def reorder_questions(
    competition_id: UUID,
    question_orders: List[Dict[str, Any]],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Reorder competition questions"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can reorder questions")
    
    return {
        "questions": reorder_competition_questions(db, competition_id, question_orders, current_user.id)
    }


@router.post("/competitions/{competition_id}/questions/generate-ai")
def generate_ai_questions_route(
    competition_id: UUID,
    prompt: str,
    num_questions: int = Query(..., ge=1, le=20, description="Number of questions to generate"),
    question_type: str = Query(..., description="Type of questions (mcq, short_answer, etc.)"),
    difficulty: str = Query("medium", description="Difficulty level"),
    subject: str = Query(..., description="Subject area"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Generate AI questions for competition"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can generate questions")
    
    return {
        "generated_questions": generate_ai_questions(
            db, competition_id, prompt, num_questions, question_type, difficulty, subject, current_user.id
        )
    }


@router.post("/competitions/{competition_id}/questions/bulk-create")
def bulk_create_questions(
    competition_id: UUID,
    questions: List[Dict[str, Any]],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create multiple questions at once"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can create questions")
    
    if not questions or len(questions) == 0:
        raise HTTPException(status_code=400, detail="At least one question is required")
    
    if len(questions) > 50:
        raise HTTPException(status_code=400, detail="Maximum 50 questions can be created at once")
    
    created_questions = []
    for question_data in questions:
        try:
            question_create = CompetitionQuestionCreate(**question_data)
            created_question = create_competition_question(db, competition_id, question_create, current_user.id)
            created_questions.append(created_question)
        except Exception as e:
            # If any question fails, continue with others but log the error
            print(f"Failed to create question: {e}")
            continue
    
    return {
        "created_questions": created_questions,
        "total_created": len(created_questions),
        "total_requested": len(questions)
    }


@router.get("/competitions/{competition_id}/questions/statistics")
def get_question_statistics(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get statistics about competition questions"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can view question statistics")
    
    from Models.Competitions import CompetitionQuestion
    from sqlalchemy import func
    
    # Get basic statistics
    total_questions = db.query(CompetitionQuestion).filter(
        CompetitionQuestion.competition_id == competition_id
    ).count()
    
    # Questions by type
    questions_by_type = db.query(
        CompetitionQuestion.question_type,
        func.count(CompetitionQuestion.id)
    ).filter(
        CompetitionQuestion.competition_id == competition_id
    ).group_by(CompetitionQuestion.question_type).all()
    
    # Questions by difficulty
    questions_by_difficulty = db.query(
        CompetitionQuestion.difficulty,
        func.count(CompetitionQuestion.id)
    ).filter(
        CompetitionQuestion.competition_id == competition_id
    ).group_by(CompetitionQuestion.difficulty).all()
    
    # AI generated vs manual
    ai_generated_count = db.query(CompetitionQuestion).filter(
        CompetitionQuestion.competition_id == competition_id,
        CompetitionQuestion.is_ai_generated == True
    ).count()
    
    # Total marks
    total_marks = db.query(func.sum(CompetitionQuestion.marks)).filter(
        CompetitionQuestion.competition_id == competition_id
    ).scalar() or 0
    
    # Average estimated time
    avg_time = db.query(func.avg(CompetitionQuestion.estimated_time_minutes)).filter(
        CompetitionQuestion.competition_id == competition_id,
        CompetitionQuestion.estimated_time_minutes.isnot(None)
    ).scalar() or 0
    
    return {
        "total_questions": total_questions,
        "total_marks": total_marks,
        "average_estimated_time_minutes": float(avg_time),
        "ai_generated_count": ai_generated_count,
        "manual_count": total_questions - ai_generated_count,
        "questions_by_type": [
            {"type": qtype.value, "count": count} for qtype, count in questions_by_type
        ],
        "questions_by_difficulty": [
            {"difficulty": diff.value, "count": count} for diff, count in questions_by_difficulty
        ]
    }


@router.post("/competitions/{competition_id}/questions/import")
def import_questions_from_exam(
    competition_id: UUID,
    exam_id: UUID,
    question_ids: List[UUID] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Import questions from an existing exam"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can import questions")
    
    from Models.Exam import Exam
    from Models.Questions import Question
    
    # Verify exam exists
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")
    
    # Get questions to import
    if question_ids:
        questions = db.query(Question).filter(
            Question.id.in_(question_ids),
            Question.exam_id == exam_id
        ).all()
    else:
        questions = exam.questions
    
    if not questions:
        raise HTTPException(status_code=404, detail="No questions found to import")
    
    imported_questions = []
    for question in questions:
        try:
            # Convert exam question to competition question format
            question_data = CompetitionQuestionCreate(
                question_text=question.text,
                question_type="mcq" if question.Type.value == "mcq" else "short_answer",
                difficulty="medium",  # Default difficulty
                marks=question.marks,
                image_url=question.imageUrl,
                options=[opt.option_text for opt in question.options] if question.options else None,
                correct_option=0 if question.options else None,  # TODO: Determine correct option
                subject=question.subject if hasattr(question, 'subject') else None
            )
            
            imported_question = create_competition_question(db, competition_id, question_data, current_user.id)
            imported_questions.append(imported_question)
        except Exception as e:
            print(f"Failed to import question {question.id}: {e}")
            continue
    
    return {
        "imported_questions": imported_questions,
        "total_imported": len(imported_questions),
        "total_available": len(questions)
    }
