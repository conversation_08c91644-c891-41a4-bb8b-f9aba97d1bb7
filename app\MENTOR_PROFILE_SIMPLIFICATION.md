# Mentor Profile Simplification

## Overview
The MentorProfile model has been simplified to include only the essential fields as requested. This document outlines the changes made and how to use the new structure.

## Fields Kept

### Core Profile Information
- `profile_image_url` - Profile picture URL
- `bio` - Professional bio/description
- `experience_years` - Years of experience
- `hourly_rate` - Hourly rate for mentoring

### Subject Relationships (Many-to-Many)
- `expertise_subjects` - Subjects the mentor has expertise in
- `preferred_subjects` - Subjects the mentor prefers to teach

### Additional Information
- `languages` - Languages spoken (stored as JSON array)
- `availability_hours` - Available time slots (stored as JSON object)

## Database Changes

### New Tables Created
1. `mentor_expertise_subjects` - Many-to-many relationship between mentors and their expertise subjects
2. `mentor_preferred_subjects` - Many-to-many relationship between mentors and their preferred subjects

### Fields Removed
- `full_name` (moved to User model if needed)
- `expertise_areas` (replaced with many-to-many relationship)
- `education`
- `certifications`
- `phone`
- `linkedin_url`
- `website`
- `current_position`
- `current_organization`
- `preferred_subjects` (replaced with many-to-many relationship)
- `is_verified`
- `verification_status`
- `verification_notes`
- `verified_at`
- `verified_by`
- `resume_url`
- `portfolio_url`
- `rating`
- `total_reviews`
- `competitions_checked`

## Schema Changes

### MentorRegistrationCreate
```python
class MentorRegistrationCreate(UserCreate):
    # Essential mentor profile details
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    
    # Subject relationships (UUIDs)
    expertise_subject_ids: List[UUID] = Field(default=[])
    preferred_subject_ids: List[UUID] = Field(default=[])
    
    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None
```

### MentorProfileOut
```python
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    availability_hours: Optional[Dict[str, Any]]
    profile_image_url: Optional[str]
    
    # Subject relationships
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None
    
    created_at: datetime
    updated_at: datetime
```

## Usage Examples

### Creating a Mentor
```python
mentor_data = MentorRegistrationCreate(
    username="mentor123",
    email="<EMAIL>",
    mobile="+1234567890",
    password="securepassword",
    country="USA",
    bio="Experienced STEM mentor",
    experience_years=5,
    hourly_rate=50.00,
    expertise_subject_ids=[math_subject_id, physics_subject_id],
    preferred_subject_ids=[math_subject_id, chemistry_subject_id],
    languages=["English", "Spanish"],
    availability_hours={
        "monday": ["09:00-12:00", "14:00-17:00"],
        "tuesday": ["09:00-12:00"]
    }
)
```

### Updating a Mentor Profile
```python
update_data = MentorProfileUpdate(
    bio="Updated bio",
    hourly_rate=60.00,
    expertise_subject_ids=[new_subject_id],
    languages=["English", "French"]
)
```

## Migration

### Running the Migration
```bash
python migrations/simplify_mentor_profile.py
```

### Rolling Back (if needed)
```bash
python migrations/simplify_mentor_profile.py rollback
```

## Testing

A test script is provided to verify the new model works correctly:
```bash
python test_simplified_mentor.py
```

## Benefits of Simplification

1. **Cleaner Data Model** - Only essential fields are kept
2. **Proper Relationships** - Subject relationships are now properly normalized
3. **Better Performance** - Fewer fields mean faster queries
4. **Easier Maintenance** - Less complex model is easier to maintain
5. **Flexible Subject Management** - Easy to add/remove subject relationships

## API Endpoints

### Public Mentor Endpoints

#### Register New Mentor
```http
POST /api/mentors/register
Content-Type: application/json

{
  "username": "mentor123",
  "email": "<EMAIL>",
  "mobile": "+1234567890",
  "password": "securepassword",
  "country": "USA",
  "bio": "Experienced STEM mentor",
  "experience_years": 5,
  "hourly_rate": 50.00,
  "expertise_subject_ids": ["uuid1", "uuid2"],
  "preferred_subject_ids": ["uuid1", "uuid3"],
  "languages": ["English", "Spanish"],
  "availability_hours": {
    "monday": ["09:00-12:00", "14:00-17:00"],
    "tuesday": ["09:00-12:00"]
  }
}
```

#### Get Mentors List
```http
GET /api/mentors/list?search=math&country=USA&verified_only=true&page=1&size=20
Authorization: Bearer <token>
```

#### Get Mentor by ID
```http
GET /api/mentors/{mentor_id}
Authorization: Bearer <token>
```

### Authenticated Mentor Endpoints

#### Get My Profile
```http
GET /api/mentors/profile
Authorization: Bearer <token>
```

#### Update My Profile
```http
PUT /api/mentors/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "bio": "Updated bio",
  "experience_years": 6,
  "hourly_rate": 60.00,
  "expertise_subject_ids": ["uuid1", "uuid4"],
  "preferred_subject_ids": ["uuid1", "uuid2"],
  "languages": ["English", "French"],
  "availability_hours": {
    "monday": ["10:00-15:00"],
    "wednesday": ["14:00-18:00"]
  },
  "profile_image_url": "https://example.com/image.jpg"
}
```

### Mentor-Institute Association Endpoints

#### Apply to Institute
```http
POST /api/mentors/apply-to-institute
Authorization: Bearer <token>
Content-Type: application/json

{
  "institute_id": "institute-uuid",
  "application_message": "I would like to join your institute...",
  "proposed_hourly_rate": 55.00
}
```

#### Invite Mentor (Institute Only)
```http
POST /api/mentors/invite-mentor
Authorization: Bearer <token>
Content-Type: application/json

{
  "mentor_id": "mentor-uuid",
  "invitation_message": "We invite you to join our institute...",
  "offered_hourly_rate": 60.00
}
```

#### Respond to Association
```http
POST /api/mentors/associations/{association_id}/respond
Authorization: Bearer <token>
Content-Type: application/json

{
  "response_message": "I accept your invitation...",
  "accept": true,
  "negotiated_rate": 58.00,
  "contract_terms": "Standard terms apply"
}
```

#### Get My Associations
```http
GET /api/mentors/associations?status=active&skip=0&limit=20
Authorization: Bearer <token>
```

#### Get Institute Mentors
```http
GET /api/mentors/institute/{institute_id}/mentors?status=active&skip=0&limit=50
Authorization: Bearer <token>
```

### Institute Mentor Management Endpoints

#### Get Mentor Applications
```http
GET /api/institute/mentors/applications?status=pending&skip=0&limit=20
Authorization: Bearer <token>
```

#### Approve Mentor Application
```http
POST /api/institute/mentors/applications/{app_id}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "approval_message": "Welcome to our institute!",
  "hourly_rate": 55.00,
  "contract_terms": "Standard contract terms"
}
```

#### Reject Mentor Application
```http
POST /api/institute/mentors/applications/{app_id}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "rejection_message": "Thank you for your interest, but...",
  "rejection_reason": "Requirements not met"
}
```

#### Get Institute Mentors (Management)
```http
GET /api/institute/mentors/manage?status=active&search=john&skip=0&limit=20
Authorization: Bearer <token>
```

#### Get Active Institute Mentors
```http
GET /api/institute/mentors?skip=0&limit=20
Authorization: Bearer <token>
```

#### Update Mentor Details
```http
PUT /api/institute/mentors/{mentor_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "hourly_rate": 65.00,
  "contract_terms": "Updated terms",
  "notes": "Performance notes"
}
```

#### Activate/Deactivate Mentor
```http
POST /api/institute/mentors/{mentor_id}/activate
Authorization: Bearer <token>
Content-Type: application/json

{
  "is_active": true,
  "reason": "Reactivating after review"
}
```

#### Get Mentor Performance
```http
GET /api/institute/mentors/{mentor_id}/performance
Authorization: Bearer <token>
```

### Competition Mentor Checking Endpoints

#### Assign Mentor to Competition
```http
POST /api/mentor-checking/competitions/{competition_id}/assign-mentor
Authorization: Bearer <token>
Content-Type: application/json

{
  "mentor_id": "mentor-uuid",
  "assignment_message": "Please check these submissions",
  "estimated_hours": 10.5,
  "special_instructions": "Focus on mathematical accuracy"
}
```

#### Get Mentor Assignments
```http
GET /api/mentor-checking/mentor/assignments?status=assigned&skip=0&limit=20
Authorization: Bearer <token>
```

#### Respond to Assignment
```http
POST /api/mentor-checking/assignments/{assignment_id}/respond
Authorization: Bearer <token>
Content-Type: application/json

{
  "accept": true,
  "response_message": "I accept this assignment",
  "estimated_completion_date": "2024-01-15T10:00:00Z"
}
```

#### Get Answers to Check
```http
GET /api/mentor-checking/mentor/answers-to-check?competition_id=uuid&skip=0&limit=20
Authorization: Bearer <token>
```

#### Submit Mentor Score
```http
POST /api/mentor-checking/answers/{answer_id}/score
Authorization: Bearer <token>
Content-Type: application/json

{
  "score": 85.5,
  "feedback": "Good work, but could improve on...",
  "detailed_comments": "Detailed feedback here"
}
```

#### Get Mentor Statistics
```http
GET /api/mentor-checking/mentor/statistics?competition_id=uuid
Authorization: Bearer <token>
```

#### Get Mentor Competition Dashboard
```http
GET /api/mentor-checking/competitions/{competition_id}/mentor/dashboard
Authorization: Bearer <token>
```

#### Get Available Competitions
```http
GET /api/mentor-checking/mentor/available-competitions
Authorization: Bearer <token>
```

### Response Examples

#### Mentor Profile Response
```json
{
  "id": "mentor-uuid",
  "user_id": "user-uuid",
  "bio": "Experienced STEM mentor with 5 years of experience",
  "experience_years": 5,
  "hourly_rate": 50.00,
  "languages": ["English", "Spanish"],
  "availability_hours": {
    "monday": ["09:00-12:00", "14:00-17:00"],
    "tuesday": ["09:00-12:00"]
  },
  "profile_image_url": "https://example.com/profile.jpg",
  "expertise_subjects": [
    {"id": "uuid1", "name": "Mathematics"},
    {"id": "uuid2", "name": "Physics"}
  ],
  "preferred_subjects": [
    {"id": "uuid1", "name": "Mathematics"},
    {"id": "uuid3", "name": "Chemistry"}
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Mentors List Response
```json
{
  "mentors": [
    {
      "id": "mentor-uuid",
      "user": {
        "username": "mentor123",
        "email": "<EMAIL>",
        "country": "USA"
      },
      "bio": "Experienced mentor",
      "experience_years": 5,
      "hourly_rate": 50.00,
      "expertise_subjects": ["Mathematics", "Physics"],
      "rating": 4.8
    }
  ],
  "total": 25,
  "page": 1,
  "size": 20,
  "has_next": true,
  "has_prev": false
}
```

## Notes

- The `languages` field is stored as JSON to allow multiple languages
- The `availability_hours` field is stored as JSON to allow flexible scheduling
- Subject relationships use proper many-to-many tables for better data integrity
- All removed fields are backed up in `mentor_profile_backup` table during migration
- All endpoints require appropriate authentication and authorization
- Institute-specific endpoints require institute user type
- Mentor-specific endpoints require mentor user type
