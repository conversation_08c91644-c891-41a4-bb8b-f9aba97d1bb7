import enum
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Table, Boolean, JSON, Text, Numeric, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime, timezone
import uuid


class EventCategoryEnum(enum.Enum):
    WORKSHOP = "workshop"
    CONFERENCE = "conference"
    WEBINAR = "webinar"
    COMPETITION = "competition"


class EventStatusEnum(enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


class TicketStatusEnum(enum.Enum):
    ACTIVE = "active"
    SOLD_OUT = "sold_out"
    INACTIVE = "inactive"


class RegistrationStatusEnum(enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    ATTENDED = "attended"


class PaymentStatusEnum(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"


# Association table for Event <-> Speaker many-to-many
event_speaker_association = Table(
    'event_speaker_association', BaseModel.metadata,
    Column('event_id', UUID(as_uuid=True), ForeignKey('events.id'), primary_key=True),
    Column('speaker_id', UUID(as_uuid=True), ForeignKey('event_speakers.id'), primary_key=True)
)


class EventCategory(BaseModel):
    __tablename__ = 'event_categories'
    
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # Hex color code
    icon = Column(String(50), nullable=True)  # Icon name or class
    is_active = Column(Boolean, default=True)
    
    # Relationships
    events = relationship('Event', back_populates='category')


class EventLocation(BaseModel):
    __tablename__ = 'event_locations'
    
    name = Column(String(200), nullable=False)
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    latitude = Column(Numeric(10, 8), nullable=True)
    longitude = Column(Numeric(11, 8), nullable=True)
    capacity = Column(Integer, nullable=True)
    facilities = Column(JSON, nullable=True)  # List of available facilities
    contact_info = Column(JSON, nullable=True)  # Contact details
    is_virtual = Column(Boolean, default=False)
    virtual_link = Column(String(500), nullable=True)
    
    # Relationships
    events = relationship('Event', back_populates='location')


class EventSpeaker(BaseModel):
    __tablename__ = 'event_speakers'
    
    name = Column(String(200), nullable=False)
    title = Column(String(200), nullable=True)
    bio = Column(Text, nullable=True)
    profile_image_url = Column(String(500), nullable=True)
    company = Column(String(200), nullable=True)
    website = Column(String(500), nullable=True)
    linkedin_url = Column(String(500), nullable=True)
    twitter_url = Column(String(500), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    expertise_areas = Column(JSON, nullable=True)  # List of expertise areas
    is_featured = Column(Boolean, default=False)
    
    # Relationships
    events = relationship(
        'Event',
        secondary=event_speaker_association,
        back_populates='speakers'
    )


class Event(BaseModel):
    __tablename__ = 'events'
    
    title = Column(String(300), nullable=False)
    description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)
    banner_image_url = Column(String(500), nullable=True)
    gallery_images = Column(JSON, nullable=True)  # List of image URLs
    
    # Timing
    start_datetime = Column(DateTime(timezone=True), nullable=False)
    end_datetime = Column(DateTime(timezone=True), nullable=False)
    registration_start = Column(DateTime(timezone=True), nullable=True)
    registration_end = Column(DateTime(timezone=True), nullable=True)
    
    # Event details
    category_id = Column(UUID(as_uuid=True), ForeignKey('event_categories.id'), nullable=False)
    location_id = Column(UUID(as_uuid=True), ForeignKey('event_locations.id'), nullable=True)
    organizer_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    institute_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # For institute-organized events
    
    # Status and settings
    status = Column(Enum(EventStatusEnum), default=EventStatusEnum.DRAFT)
    is_featured = Column(Boolean, default=False)
    is_public = Column(Boolean, default=True)
    requires_approval = Column(Boolean, default=False)
    max_attendees = Column(Integer, nullable=True)
    min_attendees = Column(Integer, nullable=True)
    
    # Additional info
    agenda = Column(JSON, nullable=True)  # Event agenda/schedule
    requirements = Column(Text, nullable=True)  # Prerequisites or requirements
    tags = Column(JSON, nullable=True)  # List of tags
    external_links = Column(JSON, nullable=True)  # Additional links
    
    # Competition specific fields (for competition events)
    is_competition = Column(Boolean, default=False)
    competition_exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id'), nullable=True)
    competition_rules = Column(Text, nullable=True)
    prize_details = Column(JSON, nullable=True)  # Prize information

    # Competition settings
    competition_duration_minutes = Column(Integer, nullable=True)
    max_attempts = Column(Integer, default=1)
    passing_score = Column(Numeric(5, 2), nullable=True)

    # Security settings for competitions
    enable_proctoring = Column(Boolean, default=False)
    enable_screen_recording = Column(Boolean, default=False)
    enable_webcam = Column(Boolean, default=False)
    disable_copy_paste = Column(Boolean, default=True)
    randomize_questions = Column(Boolean, default=True)

    # Checking settings
    auto_check_mcq = Column(Boolean, default=True)
    require_mentor_check = Column(Boolean, default=True)
    mentor_check_deadline = Column(DateTime, nullable=True)
    
    # Relationships
    category = relationship('EventCategory', back_populates='events')
    location = relationship('EventLocation', back_populates='events')
    organizer = relationship('User', foreign_keys=[organizer_id], backref='organized_events')
    institute = relationship('User', foreign_keys=[institute_id], overlaps="competitions")
    speakers = relationship(
        'EventSpeaker',
        secondary=event_speaker_association,
        back_populates='events'
    )
    tickets = relationship('EventTicket', back_populates='event', cascade='all, delete-orphan')
    registrations = relationship('EventRegistration', back_populates='event', cascade='all, delete-orphan')
    competition_exam = relationship('Exam', backref='competition_events')

    # Competition-specific relationships
    competition_questions = relationship('CompetitionQuestion', back_populates='competition', cascade='all, delete-orphan')
    competition_answers = relationship('CompetitionAnswer', foreign_keys='CompetitionAnswer.competition_id', cascade='all, delete-orphan', overlaps="competition")
    competition_sessions = relationship('CompetitionSession', foreign_keys='CompetitionSession.competition_id', cascade='all, delete-orphan', overlaps="competition")
    competition_results = relationship('CompetitionResult', foreign_keys='CompetitionResult.competition_id', cascade='all, delete-orphan', overlaps="competition")
    mentor_assignments = relationship('CompetitionMentorAssignment', foreign_keys='CompetitionMentorAssignment.competition_id', cascade='all, delete-orphan', overlaps="competition,mentor")


class EventTicket(BaseModel):
    __tablename__ = 'event_tickets'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    name = Column(String(200), nullable=False)  # e.g., "Early Bird", "VIP", "Student"
    description = Column(Text, nullable=True)
    price = Column(Numeric(10, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default='USD')

    # Availability
    total_quantity = Column(Integer, nullable=True)  # null = unlimited
    sold_quantity = Column(Integer, default=0)
    status = Column(Enum(TicketStatusEnum), default=TicketStatusEnum.ACTIVE)

    # Timing
    sale_start = Column(DateTime(timezone=True), nullable=True)
    sale_end = Column(DateTime(timezone=True), nullable=True)

    # Restrictions
    min_quantity_per_order = Column(Integer, default=1)
    max_quantity_per_order = Column(Integer, nullable=True)
    requires_approval = Column(Boolean, default=False)

    # Additional settings
    is_transferable = Column(Boolean, default=True)
    is_refundable = Column(Boolean, default=False)
    refund_policy = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)

    # Relationships
    event = relationship('Event', back_populates='tickets')
    registrations = relationship('EventRegistration', back_populates='ticket')


class EventRegistration(BaseModel):
    __tablename__ = 'event_registrations'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    ticket_id = Column(UUID(as_uuid=True), ForeignKey('event_tickets.id'), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)

    # Registration details
    registration_number = Column(String(50), nullable=False, unique=True)
    status = Column(Enum(RegistrationStatusEnum), default=RegistrationStatusEnum.PENDING)
    quantity = Column(Integer, default=1)
    total_amount = Column(Numeric(10, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default='USD')

    # Attendee information
    attendee_info = Column(JSON, nullable=True)  # Additional attendee details
    special_requirements = Column(Text, nullable=True)
    emergency_contact = Column(JSON, nullable=True)

    # Timestamps
    registered_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    attended_at = Column(DateTime(timezone=True), nullable=True)

    # Payment tracking
    payment_status = Column(Enum(PaymentStatusEnum), default=PaymentStatusEnum.PENDING)
    payment_reference = Column(String(255), nullable=True)
    payment_method = Column(String(50), nullable=True)

    # Relationships
    event = relationship('Event', back_populates='registrations')
    ticket = relationship('EventTicket', back_populates='registrations')
    user = relationship('User', backref='event_registrations')
    payments = relationship('EventPayment', back_populates='registration', cascade='all, delete-orphan')


class EventPayment(BaseModel):
    __tablename__ = 'event_payments'

    registration_id = Column(UUID(as_uuid=True), ForeignKey('event_registrations.id'), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')

    # Payment details
    payment_method = Column(String(50), nullable=False)  # stripe, paypal, bank_transfer, etc.
    payment_gateway = Column(String(50), nullable=True)
    gateway_transaction_id = Column(String(255), nullable=True)
    gateway_payment_intent_id = Column(String(255), nullable=True)

    # Status and timing
    status = Column(Enum(PaymentStatusEnum), default=PaymentStatusEnum.PENDING)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    refunded_at = Column(DateTime(timezone=True), nullable=True)

    # Additional info
    failure_reason = Column(Text, nullable=True)
    refund_reason = Column(Text, nullable=True)
    refund_amount = Column(Numeric(10, 2), nullable=True)
    gateway_response = Column(JSON, nullable=True)  # Store gateway response

    # Relationships
    registration = relationship('EventRegistration', back_populates='payments')


class EventAttendance(BaseModel):
    __tablename__ = 'event_attendance'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    registration_id = Column(UUID(as_uuid=True), ForeignKey('event_registrations.id'), nullable=True)

    # Attendance details
    checked_in_at = Column(DateTime(timezone=True), nullable=True)
    checked_out_at = Column(DateTime(timezone=True), nullable=True)
    attendance_method = Column(String(50), nullable=True)  # qr_code, manual, etc.
    notes = Column(Text, nullable=True)

    # Relationships
    event = relationship('Event', backref='attendance_records')
    user = relationship('User', backref='event_attendance')
    registration = relationship('EventRegistration', backref='attendance_record')


class EventFeedback(BaseModel):
    __tablename__ = 'event_feedback'

    event_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)

    # Feedback details
    rating = Column(Integer, nullable=True)  # 1-5 scale
    review = Column(Text, nullable=True)
    would_recommend = Column(Boolean, nullable=True)

    # Specific ratings
    content_rating = Column(Integer, nullable=True)
    speaker_rating = Column(Integer, nullable=True)
    venue_rating = Column(Integer, nullable=True)
    organization_rating = Column(Integer, nullable=True)

    # Additional feedback
    suggestions = Column(Text, nullable=True)
    is_public = Column(Boolean, default=True)
    is_approved = Column(Boolean, default=False)

    # Relationships
    event = relationship('Event', backref='feedback')
    user = relationship('User', backref='event_feedback')
