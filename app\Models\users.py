import datetime
import enum
from sqlalchemy import <PERSON>umn, DateTime, Integer, String, Boolean, Enum, ForeignKey, Text, LargeBinary, Float, CheckConstraint, Numeric, UniqueConstraint, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, foreign
from .baseModel import BaseModel
import uuid

# Many-to-many association table for teachers and subjects
teacher_subjects = Table(
    'teacher_subjects',
    BaseModel.metadata,
    Column('teacher_profile_id', UUID(as_uuid=True), ForeignKey('teacher_profiles.id'), primary_key=True),
    Column('subject_id', UUID(as_uuid=True), ForeignKey('subjects.id'), primary_key=True)
)

# Many-to-many association table for mentor expertise subjects
mentor_expertise_subjects = Table(
    'mentor_expertise_subjects',
    BaseModel.metadata,
    Column('mentor_profile_id', UUID(as_uuid=True), ForeignKey('mentor_profile.user_id'), primary_key=True),
    Column('subject_id', UUID(as_uuid=True), Foreign<PERSON><PERSON>('subjects.id'), primary_key=True)
)

# Many-to-many association table for mentor preferred subjects
mentor_preferred_subjects = Table(
    'mentor_preferred_subjects',
    BaseModel.metadata,
    Column('mentor_profile_id', UUID(as_uuid=True), ForeignKey('mentor_profile.user_id'), primary_key=True),
    Column('subject_id', UUID(as_uuid=True), ForeignKey('subjects.id'), primary_key=True)
)

# --- Enum for User Type ---
class UserTypeEnum(enum.Enum):
    admin = "admin"
    student = "student"
    teacher = "teacher"
    sponsor = "sponsor"
    institute = "institute"
    mentor = "mentor"

# --- User Table ---
class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    mobile = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=False)
    country = Column(String, nullable=True)
    profile_picture = Column(String, nullable=True)

    user_type = Column(Enum(UserTypeEnum), nullable=False)
    is_email_verified = Column(Boolean, default=False)
    is_mobile_verified = Column(Boolean, default=False)

    # Relationships
    sponsor_profile = relationship("SponsorProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    institute_profile = relationship("InstituteProfile", back_populates="user", uselist=False, cascade="all, delete-orphan", foreign_keys="InstituteProfile.user_id")
    mentor_profile = relationship("MentorProfile", back_populates="user", uselist=False, cascade="all, delete-orphan", foreign_keys="MentorProfile.user_id")
    cnic = relationship("CNIC", back_populates="user", uselist=False, cascade="all, delete-orphan")
    passport = relationship("Passport", back_populates="user", uselist=False, cascade="all, delete-orphan")
    # inside User class
    tasks = relationship('TaskStudents', back_populates='student', cascade='all, delete-orphan')
    task_classrooms = relationship('TaskClassroomStudent', back_populates='student', cascade='all, delete-orphan')
    task_attachments = relationship('TaskAttachment', back_populates='student', cascade='all, delete-orphan')
    student_task_attachments = relationship('StudentTaskAttachment', back_populates='student', cascade='all, delete-orphan')
    teacher_profile = relationship("TeacherProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # Subscription relationship
    subscription = relationship("UserSubscription", back_populates="user", uselist=False)

# --- CNIC Table ---
class CNIC(BaseModel):
    __tablename__ = "cnic"

    encrypted_cnic = Column(String, nullable=False)
    cnic_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="cnic")


# --- Passport Table ---
class Passport(BaseModel):
    __tablename__ = "passport"

    encrypted_passport = Column(String, nullable=False)
    passport_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="passport")


# --- Subject Table ---
class Subject(BaseModel):
    __tablename__ = "subjects"

    id = Column(UUID(as_uuid=True), primary_key=True)
    name = Column(String, nullable=False, unique=True)

    tasks = relationship("Task", back_populates="subject", cascade="all, delete-orphan")
    chapters = relationship("Chapter", back_populates="subject", cascade="all, delete-orphan")
    questions = relationship('Question', back_populates='subject')

    # Many-to-many relationship with teachers
    teachers = relationship("TeacherProfile", secondary="teacher_subjects", back_populates="subjects")

    # Many-to-many relationships with mentors
    mentor_expertise = relationship("MentorProfile", secondary="mentor_expertise_subjects", back_populates="expertise_subjects")
    mentor_preferences = relationship("MentorProfile", secondary="mentor_preferred_subjects", back_populates="preferred_subjects")


# --- Sponsor Profile ---
class SponsorProfile(BaseModel):
    __tablename__ = "sponsor_profile"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    company_name = Column(String, nullable=True)
    logo_url = Column(String, nullable=True)
    description = Column(String, nullable=True)

    user = relationship("User", back_populates="sponsor_profile")


# --- Institute Profile ---
class InstituteProfile(BaseModel):
    __tablename__ = "institute_profile"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    institute_name = Column(String, nullable=False)
    address = Column(Text, nullable=True)
    accreditation = Column(String, nullable=True)

    # Enhanced institute information
    description = Column(Text, nullable=True)
    website = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    email = Column(String, nullable=True)
    established_year = Column(Integer, nullable=True)
    institute_type = Column(String, nullable=True)  # university, college, school, training_center, etc.

    # Location details
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    country = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)

    # Status and verification
    is_verified = Column(Boolean, default=False)
    verification_status = Column(String, default="pending")  # pending, approved, rejected
    verification_notes = Column(Text, nullable=True)
    verified_at = Column(DateTime, nullable=True)
    verified_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Social media and contact
    linkedin_url = Column(String, nullable=True)
    facebook_url = Column(String, nullable=True)
    twitter_url = Column(String, nullable=True)

    # Logo and branding
    logo_url = Column(String, nullable=True)
    banner_url = Column(String, nullable=True)

    user = relationship("User", back_populates="institute_profile", foreign_keys=[user_id])
    verifier = relationship("User", foreign_keys=[verified_by])

    # Relationships with mentors and competitions
    mentor_associations = relationship(
        "MentorInstituteAssociation",
        primaryjoin="InstituteProfile.user_id == foreign(MentorInstituteAssociation.institute_id)",
        cascade="all, delete-orphan"
    )
    competitions = relationship(
        "Event",
        primaryjoin="InstituteProfile.user_id == foreign(Event.institute_id)"
    )


# --- Mentor Profile ---
class MentorProfile(BaseModel):
    __tablename__ = "mentor_profile"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    # Essential profile information
    profile_image_url = Column(String, nullable=True)
    bio = Column(Text, nullable=True)
    experience_years = Column(Integer, nullable=True)
    hourly_rate = Column(Numeric(10, 2), nullable=True)

    # Languages (JSON string)
    languages = Column(Text, nullable=True)  # JSON string of languages

    # Availability times (JSON string)
    availability_hours = Column(Text, nullable=True)  # JSON string of available hours

    # Relationships
    user = relationship("User", back_populates="mentor_profile", foreign_keys=[user_id])

    # Many-to-many relationships with subjects
    expertise_subjects = relationship("Subject", secondary="mentor_expertise_subjects", back_populates="mentor_expertise")
    preferred_subjects = relationship("Subject", secondary="mentor_preferred_subjects", back_populates="mentor_preferences")

    # Existing relationships (keeping for compatibility)
    institute_associations = relationship(
        "MentorInstituteAssociation",
        primaryjoin="MentorProfile.user_id == foreign(MentorInstituteAssociation.mentor_id)",
        cascade="all, delete-orphan"
    )
    competition_assignments = relationship(
        "CompetitionMentorAssignment",
        primaryjoin="MentorProfile.user_id == foreign(CompetitionMentorAssignment.mentor_id)",
        cascade="all, delete-orphan"
    )


# --- Mentor-Institute Association ---
class MentorInstituteAssociation(BaseModel):
    __tablename__ = "mentor_institute_association"

    mentor_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    institute_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Association details
    status = Column(String, default="pending")  # pending, approved, rejected, active, inactive
    association_type = Column(String, nullable=False)  # mentor_applied, institute_invited

    # Application/Invitation details
    application_message = Column(Text, nullable=True)
    response_message = Column(Text, nullable=True)

    # Timestamps
    applied_at = Column(DateTime, default=datetime.datetime.utcnow)
    responded_at = Column(DateTime, nullable=True)
    approved_at = Column(DateTime, nullable=True)

    # Approval details
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Association terms
    hourly_rate = Column(Numeric(10, 2), nullable=True)
    contract_terms = Column(Text, nullable=True)
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)

    # Constraints
    __table_args__ = (
        UniqueConstraint('mentor_id', 'institute_id', name='unique_mentor_institute'),
    )

    mentor = relationship("User", foreign_keys=[mentor_id], overlaps="institute_associations")
    institute = relationship("User", foreign_keys=[institute_id], overlaps="mentor_associations")
    approver = relationship("User", foreign_keys=[approved_by])


class TeacherProfile(BaseModel):
    __tablename__ = 'teacher_profiles'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), unique=True, nullable=False)
    bio = Column(Text, nullable=True)
    experience_years = Column(Integer, nullable=True)
    specialization = Column(String, nullable=True)
    website = Column(String, nullable=True)
    office_hours = Column(String, nullable=True)
    rating = Column(Numeric(2, 1), CheckConstraint('rating >= 0 AND rating <= 5'), nullable=True, default=0.0)

    # Home tutoring features
    offers_home_tutoring = Column(Boolean, default=False)
    home_address = Column(Text, nullable=True)
    latitude = Column(Numeric(10, 8), nullable=True)  # For location-based search
    longitude = Column(Numeric(11, 8), nullable=True)
    tutoring_radius_km = Column(Integer, nullable=True)  # How far willing to travel
    hourly_rate_home = Column(Numeric(10, 2), nullable=True)  # Rate for home tutoring
    hourly_rate_online = Column(Numeric(10, 2), nullable=True)  # Rate for online tutoring

    # Contact preferences
    preferred_contact_method = Column(String, nullable=True)  # phone, email, whatsapp
    whatsapp_number = Column(String, nullable=True)

    # Availability
    available_days = Column(Text, nullable=True)  # JSON string of available days
    available_hours = Column(Text, nullable=True)  # JSON string of time slots

    user = relationship("User", back_populates="teacher_profile")
    subscription = relationship("TeacherSubscription", back_populates="teacher_profile", uselist=False)

    # Many-to-many relationship with subjects
    subjects = relationship("Subject", secondary="teacher_subjects", back_populates="teachers")


# Subscription Plans for all user types
class SubscriptionPlan(BaseModel):
    __tablename__ = 'subscription_plans'

    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Integer, nullable=False)  # Price in cents
    duration_days = Column(Integer, nullable=False)
    features = Column(Text)  # JSON string of features
    is_active = Column(Boolean, default=True)

    # Plan type and target user
    plan_type = Column(String(50), nullable=False)  # basic, premium, pro, home_tutor
    target_user_type = Column(Enum(UserTypeEnum), nullable=False)

    # Plan limits and features
    max_classrooms = Column(Integer, nullable=True)  # For teachers
    max_students_per_classroom = Column(Integer, nullable=True)
    max_exams_per_month = Column(Integer, nullable=True)
    max_questions_per_exam = Column(Integer, nullable=True)
    allows_home_tutoring = Column(Boolean, default=False)
    allows_ai_question_generation = Column(Boolean, default=False)
    allows_advanced_analytics = Column(Boolean, default=False)
    priority_support = Column(Boolean, default=False)

    subscriptions = relationship("UserSubscription", back_populates="plan")


# Universal subscription model for all user types
class UserSubscription(BaseModel):
    __tablename__ = 'user_subscriptions'

    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    plan_id = Column(UUID(as_uuid=True), ForeignKey('subscription_plans.id'), nullable=True)

    start_date = Column(DateTime, default=datetime.datetime.now(datetime.UTC))
    end_date = Column(DateTime)
    status = Column(String(50), default='active')  # active, expired, cancelled, trial
    auto_renew = Column(Boolean, default=True)
    payment_reference = Column(String(255))  # Payment reference

    # Trial information
    is_trial = Column(Boolean, default=False)
    trial_end_date = Column(DateTime, nullable=True)

    # Billing information
    billing_cycle = Column(String(20), default='monthly')  # monthly, yearly
    next_billing_date = Column(DateTime, nullable=True)

    # Usage tracking
    current_usage = Column(Text, nullable=True)  # JSON string for usage metrics

    user = relationship("User", back_populates="subscription")
    plan = relationship("SubscriptionPlan", back_populates="subscriptions")

    # Ensure one active subscription per user
    __table_args__ = (
        UniqueConstraint('user_id', name='unique_user_subscription'),
    )


# Legacy support - keep for backward compatibility
class Plan(BaseModel):
    __tablename__ = 'plans'

    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Integer, nullable=False)  # Price in cents
    duration_days = Column(Integer, nullable=False)
    features = Column(Text)  # JSON string or comma-separated features
    is_active = Column(Boolean, default=True)

    subscriptions = relationship("TeacherSubscription", back_populates="plan")


class TeacherSubscription(BaseModel):
    __tablename__ = 'teacher_subscriptions'

    teacher_profile_id = Column(UUID(as_uuid=True), ForeignKey('teacher_profiles.id'), nullable=False, unique=True)
    plan_id = Column(UUID(as_uuid=True), ForeignKey('plans.id'), nullable=True)

    start_date = Column(DateTime, default=datetime.datetime.now(datetime.UTC))
    end_date = Column(DateTime)
    status = Column(String(50), default='active')  # active, expired, cancelled
    auto_renew = Column(Boolean, default=True)
    payment_reference = Column(String(255))  # e.g. Stripe ID

    teacher_profile = relationship("TeacherProfile", back_populates="subscription")
    plan = relationship("Plan", back_populates="subscriptions")

class TeacherRating(BaseModel):
    __tablename__ = 'teacher_ratings'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    rating = Column(Numeric(2, 1), CheckConstraint('rating >= 0 AND rating <= 5'), nullable=False)
    __table_args__ = (UniqueConstraint('teacher_id', 'student_id', name='unique_teacher_student_rating'),)

    # Optionally, add relationships if needed
    # teacher = relationship('User', foreign_keys=[teacher_id])
    # student = relationship('User', foreign_keys=[student_id])
