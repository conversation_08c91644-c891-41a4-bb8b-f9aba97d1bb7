from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from datetime import datetime, timezone
from uuid import UUID

# Import services directly to avoid circular imports
from services.CalendarService import calendar_service

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user

router = APIRouter()


@router.get("/calendar")
def get_comprehensive_calendar(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    include_events: bool = Query(True, description="Include events"),
    include_exams: bool = Query(True, description="Include exams"),
    include_tasks: bool = Query(True, description="Include tasks"),
    category_filter: Optional[List[UUID]] = Query(None, description="Filter by event categories"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get comprehensive calendar view for a specific month"""
    current_user = get_current_user(token, db)
    
    return calendar_service.get_comprehensive_calendar(
        db=db,
        year=year,
        month=month,
        user_id=current_user.id,
        include_events=include_events,
        include_exams=include_exams,
        include_tasks=include_tasks,
        category_filter=category_filter
    )


@router.get("/calendar/upcoming")
def get_upcoming_items(
    days_ahead: int = Query(7, ge=1, le=30, description="Number of days to look ahead"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of items"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get upcoming calendar items for the next N days"""
    current_user = get_current_user(token, db)
    
    return {
        "items": calendar_service.get_upcoming_items(
            db=db,
            user_id=current_user.id,
            days_ahead=days_ahead,
            limit=limit
        )
    }


@router.get("/calendar/daily")
def get_daily_schedule(
    date: datetime = Query(..., description="Date for daily schedule (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get detailed schedule for a specific day"""
    current_user = get_current_user(token, db)
    
    return calendar_service.get_daily_schedule(
        db=db,
        user_id=current_user.id,
        date=date
    )


@router.get("/calendar/today")
def get_today_schedule(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get today's schedule"""
    current_user = get_current_user(token, db)
    today = datetime.now(timezone.utc)
    
    return calendar_service.get_daily_schedule(
        db=db,
        user_id=current_user.id,
        date=today
    )


@router.get("/calendar/week")
def get_weekly_schedule(
    start_date: datetime = Query(..., description="Start date of the week (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get weekly schedule starting from a specific date"""
    current_user = get_current_user(token, db)
    
    from datetime import timedelta
    
    weekly_schedule = []
    for i in range(7):
        day_date = start_date + timedelta(days=i)
        day_schedule = calendar_service.get_daily_schedule(
            db=db,
            user_id=current_user.id,
            date=day_date
        )
        weekly_schedule.append(day_schedule)
    
    return {
        "start_date": start_date.date(),
        "weekly_schedule": weekly_schedule,
        "total_items": sum(day["total_items"] for day in weekly_schedule)
    }


@router.get("/calendar/events-only")
def get_events_calendar(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    category_filter: Optional[List[UUID]] = Query(None, description="Filter by event categories"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get calendar view with events only"""
    current_user = get_current_user(token, db)
    
    return calendar_service.get_comprehensive_calendar(
        db=db,
        year=year,
        month=month,
        user_id=current_user.id,
        include_events=True,
        include_exams=False,
        include_tasks=False,
        category_filter=category_filter
    )


@router.get("/calendar/academic-only")
def get_academic_calendar(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get calendar view with academic items only (exams and tasks)"""
    current_user = get_current_user(token, db)
    
    return calendar_service.get_comprehensive_calendar(
        db=db,
        year=year,
        month=month,
        user_id=current_user.id,
        include_events=False,
        include_exams=True,
        include_tasks=True,
        category_filter=None
    )


@router.get("/calendar/summary")
def get_calendar_summary(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get calendar summary statistics for a month"""
    current_user = get_current_user(token, db)
    
    calendar_data = calendar_service.get_comprehensive_calendar(
        db=db,
        year=year,
        month=month,
        user_id=current_user.id,
        include_events=True,
        include_exams=True,
        include_tasks=True,
        category_filter=None
    )
    
    return {
        "year": year,
        "month": month,
        "summary": calendar_data["summary"]
    }


# Public calendar routes (no authentication required)
@router.get("/calendar/public-events")
def get_public_events_calendar(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    category_filter: Optional[List[UUID]] = Query(None, description="Filter by event categories"),
    db: Session = Depends(get_db)
):
    """Get public events calendar (no authentication required)"""
    from calendar import monthrange
    from Models.Events import Event, EventStatusEnum
    from sqlalchemy.orm import joinedload
    
    # Get first and last day of the month
    first_day = datetime(year, month, 1, tzinfo=timezone.utc)
    last_day_num = monthrange(year, month)[1]
    last_day = datetime(year, month, last_day_num, 23, 59, 59, tzinfo=timezone.utc)
    
    # Query public events
    query = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location)
    ).filter(
        Event.start_datetime >= first_day,
        Event.start_datetime <= last_day,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.is_public == True
    )
    
    if category_filter:
        query = query.filter(Event.category_id.in_(category_filter))
    
    events = query.all()
    
    calendar_events = []
    for event in events:
        calendar_events.append({
            "id": str(event.id),
            "title": event.title,
            "start_datetime": event.start_datetime,
            "end_datetime": event.end_datetime,
            "description": event.short_description,
            "location": event.location.name if event.location else None,
            "is_virtual": event.location.is_virtual if event.location else False,
            "category": event.category.name,
            "category_color": event.category.color,
            "is_competition": event.is_competition,
            "banner_image_url": event.banner_image_url,
            "is_featured": event.is_featured
        })
    
    return {
        "year": year,
        "month": month,
        "events": calendar_events,
        "total_events": len(calendar_events)
    }
