#!/bin/bash

# Complete Database Reset Script
# This script completely drops and recreates the database to resolve enum conflicts

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default database settings (modify as needed)
DB_NAME="${DB_NAME:-edufair_db}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"

echo -e "${BLUE}🚀 Starting complete database reset...${NC}"
echo -e "${YELLOW}⚠️  WARNING: This will completely delete all data in the database!${NC}"
echo -e "${YELLOW}Database: ${DB_NAME}${NC}"
echo -e "${YELLOW}Host: ${DB_HOST}:${DB_PORT}${NC}"
echo -e "${YELLOW}User: ${DB_USER}${NC}"

# Ask for confirmation
read -p "Are you sure you want to proceed? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo -e "${RED}❌ Operation cancelled${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 1: Stopping any active connections...${NC}"

# Kill active connections to the database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();
" || echo -e "${YELLOW}⚠️  Could not terminate connections (database might not exist)${NC}"

echo -e "${BLUE}📋 Step 2: Dropping database...${NC}"

# Drop the database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" || {
    echo -e "${RED}❌ Failed to drop database${NC}"
    exit 1
}

echo -e "${GREEN}✅ Database dropped successfully${NC}"

echo -e "${BLUE}📋 Step 3: Creating new database...${NC}"

# Create the database
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;" || {
    echo -e "${RED}❌ Failed to create database${NC}"
    exit 1
}

echo -e "${GREEN}✅ Database created successfully${NC}"

echo -e "${BLUE}📋 Step 4: Setting up database permissions...${NC}"

# Grant permissions (modify as needed)
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" || {
    echo -e "${YELLOW}⚠️  Could not grant permissions (might not be needed)${NC}"
}

echo -e "${BLUE}📋 Step 5: Running migrations...${NC}"

# Change to app directory and run migrations
cd "$(dirname "$0")/.." || {
    echo -e "${RED}❌ Could not change to app directory${NC}"
    exit 1
}

# Run alembic migrations
echo -e "${BLUE}Running alembic upgrade head...${NC}"
alembic upgrade head || {
    echo -e "${RED}❌ Alembic migration failed${NC}"
    echo -e "${YELLOW}💡 You might need to:${NC}"
    echo -e "${YELLOW}   1. Check your database connection settings${NC}"
    echo -e "${YELLOW}   2. Ensure alembic is properly configured${NC}"
    echo -e "${YELLOW}   3. Run: alembic stamp head (if needed)${NC}"
    exit 1
}

echo -e "${GREEN}✅ Migrations completed successfully${NC}"

echo -e "${BLUE}📋 Step 6: Setting up default data...${NC}"

# Run setup scripts if they exist
if [ -f "scripts/setup_default_subscriptions.py" ]; then
    echo -e "${BLUE}Setting up default subscriptions...${NC}"
    python scripts/setup_default_subscriptions.py || {
        echo -e "${YELLOW}⚠️  Could not set up default subscriptions${NC}"
    }
else
    echo -e "${YELLOW}⚠️  Default subscription setup script not found${NC}"
fi

echo -e "${GREEN}🎉 Database reset completed successfully!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo -e "${GREEN}   ✅ Database dropped and recreated${NC}"
echo -e "${GREEN}   ✅ Migrations applied${NC}"
echo -e "${GREEN}   ✅ Ready for application startup${NC}"

echo -e "${BLUE}📋 Next steps:${NC}"
echo -e "${BLUE}   1. Start your application${NC}"
echo -e "${BLUE}   2. Test database connectivity${NC}"
echo -e "${BLUE}   3. Verify all features work correctly${NC}"

echo -e "${GREEN}✅ All done!${NC}"
