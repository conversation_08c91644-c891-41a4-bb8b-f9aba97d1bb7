import redis.asyncio as redis
from fastapi import Depends
from typing import Optional

# Redis configuration
REDIS_URL = "redis://localhost:6379/0"
_redis_pool: Optional[redis.Redis] = None

async def init_redis():
    """Initialize Redis connection pool on app startup"""
    global _redis_pool
    _redis_pool = redis.from_url(REDIS_URL, decode_responses=True)
    return _redis_pool

async def close_redis():
    """Close Redis connection pool on app shutdown"""
    global _redis_pool
    if _redis_pool:
        await _redis_pool.close()
        _redis_pool = None

async def get_redis() -> redis.Redis:
    """Dependency to get Redis connection"""
    if _redis_pool is None:
        raise RuntimeError("Redis not initialized. Call init_redis() first.")
    return _redis_pool 