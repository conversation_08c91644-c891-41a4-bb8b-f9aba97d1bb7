from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from typing import Optional

from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from config.redis import get_redis
from Models.users import User
from Schemas.StudentStatistics import (
    StatisticsRequest, StatisticsResponse, QuickStatsResponse,
    StudentStatistics, QuickStats
)
from Cruds.StudentStatistics import (
    get_student_comprehensive_statistics,
    get_quick_statistics
)

router = APIRouter()


@router.get("/statistics/comprehensive", response_model=StatisticsResponse)
async def get_comprehensive_statistics(
    student_id: Optional[UUID] = Query(None, description="Student ID (optional - defaults to current user)"),
    time_period_days: int = Query(90, ge=30, le=365, description="Time period for analysis in days"),
    include_peer_comparison: bool = Query(True, description="Include peer comparison analysis"),
    include_trends: bool = Query(True, description="Include performance trend analysis"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Get comprehensive statistics for a student including:
    - Overall performance metrics
    - Subject-wise performance and rankings
    - Chapter-wise analysis
    - Peer comparison
    - Performance trends
    - Strengths and weaknesses identification
    
    **Authentication:** Requires student role authentication.
    **Note:** Students can only access their own statistics unless they have admin privileges.
    """
    try:
        # Use current user's ID if no student_id provided
        target_student_id = student_id if student_id else current_user.id
        
        # Security check: students can only access their own stats
        if current_user.user_type == "student" and target_student_id != current_user.id:
            raise HTTPException(
                status_code=403, 
                detail="Students can only access their own statistics"
            )
        
        # Get Redis client for caching
        redis_client = await get_redis()

        # Get comprehensive statistics with caching
        statistics = await get_student_comprehensive_statistics(
            db=db,
            student_id=target_student_id,
            time_period_days=time_period_days,
            include_peer_comparison=include_peer_comparison,
            include_trends=include_trends,
            redis_client=redis_client
        )
        
        return StatisticsResponse(
            success=True,
            message="Comprehensive statistics retrieved successfully",
            data=statistics
        )
        
    except ValueError as e:
        return StatisticsResponse(
            success=False,
            message=str(e),
            error_details="Student not found or invalid parameters"
        )
    except Exception as e:
        return StatisticsResponse(
            success=False,
            message="Failed to retrieve comprehensive statistics",
            error_details=str(e)
        )


@router.get("/statistics/quick", response_model=QuickStatsResponse)
async def get_quick_statistics(
    student_id: Optional[UUID] = Query(None, description="Student ID (optional - defaults to current user)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Get quick overview statistics for dashboard display:
    - Total exams taken
    - Average score
    - Current rank
    - Improvement trend
    - Last exam score
    - Next exam date (if available)
    
    **Authentication:** Requires student role authentication.
    **Note:** Students can only access their own statistics unless they have admin privileges.
    """
    try:
        # Use current user's ID if no student_id provided
        target_student_id = student_id if student_id else current_user.id
        
        # Security check: students can only access their own stats
        if current_user.user_type == "student" and target_student_id != current_user.id:
            raise HTTPException(
                status_code=403, 
                detail="Students can only access their own statistics"
            )
        
        # Get quick statistics
        quick_stats = get_quick_statistics(db=db, student_id=target_student_id)
        
        return QuickStatsResponse(
            success=True,
            data=quick_stats,
            message="Quick statistics retrieved successfully"
        )
        
    except ValueError as e:
        return QuickStatsResponse(
            success=False,
            message=str(e)
        )
    except Exception as e:
        return QuickStatsResponse(
            success=False,
            message=f"Failed to retrieve quick statistics: {str(e)}"
        )


@router.get("/statistics/subject-performance")
async def get_subject_performance(
    student_id: Optional[UUID] = Query(None, description="Student ID (optional - defaults to current user)"),
    subject_id: Optional[UUID] = Query(None, description="Specific subject ID (optional)"),
    time_period_days: int = Query(90, ge=30, le=365, description="Time period for analysis in days"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Get detailed subject-wise performance analysis:
    - Performance metrics for each subject
    - Subject rankings
    - Improvement trends per subject
    - Chapter-wise breakdown within subjects
    
    **Authentication:** Requires student role authentication.
    """
    try:
        # Use current user's ID if no student_id provided
        target_student_id = student_id if student_id else current_user.id
        
        # Security check: students can only access their own stats
        if current_user.user_type == "student" and target_student_id != current_user.id:
            raise HTTPException(
                status_code=403, 
                detail="Students can only access their own statistics"
            )
        
        # Get comprehensive statistics and extract subject performance
        statistics = get_student_comprehensive_statistics(
            db=db,
            student_id=target_student_id,
            time_period_days=time_period_days,
            include_peer_comparison=True,
            include_trends=True
        )
        
        # Filter by specific subject if requested
        subject_performance = statistics.subject_performance
        if subject_id:
            subject_performance = [
                sp for sp in subject_performance 
                if sp.subject_id == subject_id
            ]
        
        return {
            "success": True,
            "message": "Subject performance retrieved successfully",
            "data": {
                "subject_performance": subject_performance,
                "subject_rankings": statistics.subject_rankings,
                "strongest_subjects": statistics.strongest_subjects,
                "weakest_subjects": statistics.weakest_subjects,
                "last_updated": statistics.last_updated
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retrieve subject performance: {str(e)}"
        )


@router.get("/statistics/rankings")
async def get_student_rankings(
    student_id: Optional[UUID] = Query(None, description="Student ID (optional - defaults to current user)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Get detailed ranking information:
    - Overall rank among all students
    - Class rank (if applicable)
    - Subject-wise rankings
    - Percentile information
    - Peer comparison metrics
    
    **Authentication:** Requires student role authentication.
    """
    try:
        # Use current user's ID if no student_id provided
        target_student_id = student_id if student_id else current_user.id
        
        # Security check: students can only access their own stats
        if current_user.user_type == "student" and target_student_id != current_user.id:
            raise HTTPException(
                status_code=403, 
                detail="Students can only access their own statistics"
            )
        
        # Get comprehensive statistics and extract ranking data
        statistics = get_student_comprehensive_statistics(
            db=db,
            student_id=target_student_id,
            time_period_days=90,
            include_peer_comparison=True,
            include_trends=False
        )
        
        return {
            "success": True,
            "message": "Rankings retrieved successfully",
            "data": {
                "overall_ranking": statistics.ranking,
                "subject_rankings": statistics.subject_rankings,
                "peer_comparison": statistics.peer_comparison,
                "total_exams_taken": statistics.total_exams_taken,
                "overall_average": statistics.overall_average,
                "last_updated": statistics.last_updated
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retrieve rankings: {str(e)}"
        )


@router.get("/statistics/trends")
async def get_performance_trends(
    student_id: Optional[UUID] = Query(None, description="Student ID (optional - defaults to current user)"),
    time_period_days: int = Query(90, ge=30, le=365, description="Time period for trend analysis"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Get detailed performance trend analysis:
    - Performance trends over time
    - Recent exam performance
    - Improvement/decline patterns
    - Best and worst performing periods
    
    **Authentication:** Requires student role authentication.
    """
    try:
        # Use current user's ID if no student_id provided
        target_student_id = student_id if student_id else current_user.id
        
        # Security check: students can only access their own stats
        if current_user.user_type == "student" and target_student_id != current_user.id:
            raise HTTPException(
                status_code=403, 
                detail="Students can only access their own statistics"
            )
        
        # Get comprehensive statistics and extract trend data
        statistics = get_student_comprehensive_statistics(
            db=db,
            student_id=target_student_id,
            time_period_days=time_period_days,
            include_peer_comparison=False,
            include_trends=True
        )
        
        return {
            "success": True,
            "message": "Performance trends retrieved successfully",
            "data": {
                "performance_trend": statistics.performance_trend,
                "recent_exam_performance": statistics.recent_exam_performance,
                "overall_average": statistics.overall_average,
                "total_exams_taken": statistics.total_exams_taken,
                "time_period_days": time_period_days,
                "last_updated": statistics.last_updated
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retrieve performance trends: {str(e)}"
        )
