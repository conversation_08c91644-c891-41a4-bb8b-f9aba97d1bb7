from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime
from config.session import get_db
from Schemas.TeacherModule.TeacherSubscription import (
    TeacherSubscriptionCreate,
    TeacherSubscriptionOut,
    TeacherSubscriptionUpdate,
    TeacherSubscriptionList,
)
from Cruds.TeacherModule.TeacherSubscription import (
    create_teacher_subscription,
    get_teacher_subscription_by_id,
    get_teacher_subscription_by_profile_id,
    get_all_teacher_subscriptions,
    get_active_subscriptions,
    get_expired_subscriptions,
    update_teacher_subscription,
    update_teacher_subscription_by_profile_id,
    cancel_subscription,
    renew_subscription,
    delete_teacher_subscription,
    delete_teacher_subscription_by_profile_id,
)
from config.deps import oauth2_scheme
from config.permission import require_type

router = APIRouter()

@router.post("/", response_model=TeacherSubscriptionOut)
def create_subscription(
    subscription: TeacherSubscriptionCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Create a new teacher subscription"""
    return create_teacher_subscription(db, subscription)

@router.get("/{subscription_id}", response_model=TeacherSubscriptionOut)
def get_subscription_by_id(
    subscription_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get teacher subscription by ID"""
    return get_teacher_subscription_by_id(db, subscription_id)

@router.get("/profile/{profile_id}", response_model=TeacherSubscriptionOut)
def get_subscription_by_profile_id(
    profile_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get teacher subscription by profile ID"""
    return get_teacher_subscription_by_profile_id(db, profile_id)

@router.get("/", response_model=List[TeacherSubscriptionList])
def get_all_subscriptions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None, description="Filter by status"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get all teacher subscriptions with optional filtering"""
    return get_all_teacher_subscriptions(
        db, 
        skip=skip, 
        limit=limit,
        status_filter=status
    )

@router.get("/active/", response_model=List[TeacherSubscriptionList])
def get_active_subscriptions_list(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get all active teacher subscriptions"""
    return get_active_subscriptions(db)

@router.get("/expired/", response_model=List[TeacherSubscriptionList])
def get_expired_subscriptions_list(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get all expired teacher subscriptions"""
    return get_expired_subscriptions(db)

@router.put("/{subscription_id}", response_model=TeacherSubscriptionOut)
def update_subscription(
    subscription_id: UUID,
    subscription_update: TeacherSubscriptionUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Update teacher subscription by ID"""
    return update_teacher_subscription(db, subscription_id, subscription_update)

@router.put("/profile/{profile_id}", response_model=TeacherSubscriptionOut)
def update_subscription_by_profile_id(
    profile_id: UUID,
    subscription_update: TeacherSubscriptionUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Update teacher subscription by profile ID"""
    return update_teacher_subscription_by_profile_id(db, profile_id, subscription_update)

@router.patch("/{subscription_id}/cancel", response_model=TeacherSubscriptionOut)
def cancel_subscription_endpoint(
    subscription_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Cancel a teacher subscription"""
    return cancel_subscription(db, subscription_id)

@router.patch("/{subscription_id}/renew", response_model=TeacherSubscriptionOut)
def renew_subscription_endpoint(
    subscription_id: UUID,
    new_end_date: datetime,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Renew a teacher subscription"""
    return renew_subscription(db, subscription_id, new_end_date)

@router.delete("/{subscription_id}")
def delete_subscription(
    subscription_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Delete teacher subscription by ID"""
    delete_teacher_subscription(db, subscription_id)
    return {"message": "Teacher subscription deleted successfully"}

@router.delete("/profile/{profile_id}")
def delete_subscription_by_profile_id(
    profile_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Delete teacher subscription by profile ID"""
    delete_teacher_subscription_by_profile_id(db, profile_id)
    return {"message": "Teacher subscription deleted successfully"} 