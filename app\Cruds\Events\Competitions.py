from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone

# Import Models
from Models.Events import Event, EventCategory, EventLocation, EventRegistration, EventStatusEnum, RegistrationStatusEnum
from Models.Exam import Exam, StudentExamAssignment, StudentExamAttempt, StudentExamAnswer
from Models.users import User, UserTypeEnum
from Models.Questions import Question

# Import Schemas
from Schemas.Events.Events import EventOut, EventDetailedOut
from Schemas.Exams.Exam import ExamDetailedOut


def create_competition_from_exam(
    db: Session,
    exam_id: uuid.UUID,
    event_data: Dict[str, Any],
    organizer_id: uuid.UUID,
    institute_id: uuid.UUID = None
) -> EventOut:
    """Create a competition event based on an existing exam"""
    
    # Verify exam exists and organizer has access
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")
    
    # Verify organizer is a teacher or institute
    organizer = db.query(User).filter(
        User.id == organizer_id,
        User.user_type.in_([UserTypeEnum.teacher, UserTypeEnum.institute])
    ).first()
    if not organizer:
        raise HTTPException(status_code=404, detail="Organizer not found or not authorized")

    # If institute_id is provided, verify it exists and organizer has access
    if institute_id:
        institute = db.query(User).filter(
            User.id == institute_id,
            User.user_type == UserTypeEnum.institute
        ).first()
        if not institute:
            raise HTTPException(status_code=404, detail="Institute not found")

        # If organizer is not the institute itself, check if they have permission
        if organizer.user_type == UserTypeEnum.teacher and organizer_id != institute_id:
            # TODO: Add logic to check if teacher has permission to create competitions for this institute
            # For now, only allow institute to create its own competitions
            raise HTTPException(status_code=403, detail="Only institute can create competitions for itself")
    elif organizer.user_type == UserTypeEnum.institute:
        # If organizer is institute but no institute_id provided, use organizer as institute
        institute_id = organizer_id
    
    # Create competition event
    competition_event = Event(
        title=event_data.get('title', f"Competition: {exam.title}"),
        description=event_data.get('description', f"Competition based on {exam.title}"),
        short_description=event_data.get('short_description', f"Compete in {exam.title}"),
        banner_image_url=event_data.get('banner_image_url'),
        gallery_images=event_data.get('gallery_images'),
        start_datetime=event_data['start_datetime'],
        end_datetime=event_data['end_datetime'],
        registration_start=event_data.get('registration_start'),
        registration_end=event_data.get('registration_end'),
        category_id=event_data['category_id'],
        location_id=event_data.get('location_id'),
        organizer_id=organizer_id,
        institute_id=institute_id,  # Add institute ownership
        status=EventStatusEnum.DRAFT,
        is_featured=event_data.get('is_featured', False),
        is_public=event_data.get('is_public', True),
        requires_approval=event_data.get('requires_approval', False),
        max_attendees=event_data.get('max_attendees'),
        min_attendees=event_data.get('min_attendees'),
        agenda=event_data.get('agenda'),
        requirements=event_data.get('requirements'),
        tags=event_data.get('tags'),
        external_links=event_data.get('external_links'),
        is_competition=True,
        competition_exam_id=exam_id,
        competition_rules=event_data.get('competition_rules'),
        prize_details=event_data.get('prize_details'),
        # Competition-specific settings
        competition_duration_minutes=event_data.get('competition_duration_minutes'),
        max_attempts=event_data.get('max_attempts', 1),
        passing_score=event_data.get('passing_score'),
        enable_proctoring=event_data.get('enable_proctoring', False),
        enable_screen_recording=event_data.get('enable_screen_recording', False),
        enable_webcam=event_data.get('enable_webcam', False),
        disable_copy_paste=event_data.get('disable_copy_paste', True),
        randomize_questions=event_data.get('randomize_questions', True),
        auto_check_mcq=event_data.get('auto_check_mcq', True),
        require_mentor_check=event_data.get('require_mentor_check', True),
        mentor_check_deadline=event_data.get('mentor_check_deadline')
    )
    
    db.add(competition_event)
    db.commit()
    db.refresh(competition_event)
    
    return EventOut.model_validate(competition_event)


def create_competition_with_questions(
    db: Session,
    event_data: Dict[str, Any],
    questions_data: List[Dict[str, Any]],
    organizer_id: uuid.UUID,
    institute_id: uuid.UUID = None
) -> EventOut:
    """Create a competition event with custom questions"""

    # Verify organizer is a teacher or institute
    organizer = db.query(User).filter(
        User.id == organizer_id,
        User.user_type.in_([UserTypeEnum.teacher, UserTypeEnum.institute])
    ).first()
    if not organizer:
        raise HTTPException(status_code=404, detail="Organizer not found or not authorized")

    # Handle institute verification similar to create_competition_from_exam
    if institute_id:
        institute = db.query(User).filter(
            User.id == institute_id,
            User.user_type == UserTypeEnum.institute
        ).first()
        if not institute:
            raise HTTPException(status_code=404, detail="Institute not found")

        if organizer.user_type == UserTypeEnum.teacher and organizer_id != institute_id:
            raise HTTPException(status_code=403, detail="Only institute can create competitions for itself")
    elif organizer.user_type == UserTypeEnum.institute:
        institute_id = organizer_id

    # Verify category exists
    category = db.query(EventCategory).filter(EventCategory.id == event_data['category_id']).first()
    if not category:
        raise HTTPException(status_code=404, detail="Event category not found")

    # Verify location exists if provided
    if event_data.get('location_id'):
        location = db.query(EventLocation).filter(EventLocation.id == event_data['location_id']).first()
        if not location:
            raise HTTPException(status_code=404, detail="Event location not found")

    # Create competition event
    competition_event = Event(
        title=event_data['title'],
        description=event_data.get('description'),
        short_description=event_data.get('short_description'),
        banner_image_url=event_data.get('banner_image_url'),
        gallery_images=event_data.get('gallery_images'),
        start_datetime=event_data['start_datetime'],
        end_datetime=event_data['end_datetime'],
        registration_start=event_data.get('registration_start'),
        registration_end=event_data.get('registration_end'),
        category_id=event_data['category_id'],
        location_id=event_data.get('location_id'),
        organizer_id=organizer_id,
        institute_id=institute_id,
        status=EventStatusEnum.DRAFT,
        is_featured=event_data.get('is_featured', False),
        is_public=event_data.get('is_public', True),
        requires_approval=event_data.get('requires_approval', False),
        max_attendees=event_data.get('max_attendees'),
        min_attendees=event_data.get('min_attendees'),
        agenda=event_data.get('agenda'),
        requirements=event_data.get('requirements'),
        tags=event_data.get('tags'),
        external_links=event_data.get('external_links'),
        is_competition=True,
        competition_rules=event_data.get('competition_rules'),
        prize_details=event_data.get('prize_details'),
        # Competition-specific settings
        competition_duration_minutes=event_data.get('competition_duration_minutes'),
        max_attempts=event_data.get('max_attempts', 1),
        passing_score=event_data.get('passing_score'),
        enable_proctoring=event_data.get('enable_proctoring', False),
        enable_screen_recording=event_data.get('enable_screen_recording', False),
        enable_webcam=event_data.get('enable_webcam', False),
        disable_copy_paste=event_data.get('disable_copy_paste', True),
        randomize_questions=event_data.get('randomize_questions', True),
        auto_check_mcq=event_data.get('auto_check_mcq', True),
        require_mentor_check=event_data.get('require_mentor_check', True),
        mentor_check_deadline=event_data.get('mentor_check_deadline')
    )

    db.add(competition_event)
    db.flush()  # Get the event ID

    # Create competition questions
    from Models.Competitions import CompetitionQuestion
    for i, question_data in enumerate(questions_data):
        question = CompetitionQuestion(
            competition_id=competition_event.id,
            question_text=question_data['question_text'],
            question_type=question_data['question_type'],
            difficulty=question_data.get('difficulty', 'medium'),
            marks=question_data.get('marks', 1),
            image_url=question_data.get('image_url'),
            code_snippet=question_data.get('code_snippet'),
            expected_answer=question_data.get('expected_answer'),
            options=question_data.get('options'),
            correct_option=question_data.get('correct_option'),
            subject=question_data.get('subject'),
            topic=question_data.get('topic'),
            tags=question_data.get('tags'),
            question_order=i + 1,
            estimated_time_minutes=question_data.get('estimated_time_minutes')
        )
        db.add(question)

    db.commit()
    db.refresh(competition_event)

    return EventOut.model_validate(competition_event)


def get_competition_details(db: Session, event_id: uuid.UUID, user_id: uuid.UUID = None) -> Dict[str, Any]:
    """Get detailed competition information including exam details"""
    
    # Get event
    event = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location),
        joinedload(Event.speakers),
        joinedload(Event.tickets)
    ).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Check access permissions
    if not event.is_public and (not user_id or event.organizer_id != user_id):
        raise HTTPException(status_code=403, detail="Access denied to this competition")
    
    # Get exam details
    exam = None
    exam_questions_count = 0
    if event.competition_exam_id:
        exam = db.query(Exam).filter(Exam.id == event.competition_exam_id).first()
        if exam:
            exam_questions_count = len(exam.questions)
    
    # Get registration statistics
    total_registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).count()
    
    # Get competition statistics if exam exists
    competition_stats = {}
    if exam:
        # Get number of attempts
        attempts_count = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == exam.id
        ).count()
        
        # Get average score (if attempts exist)
        avg_score = 0
        if attempts_count > 0:
            # This would require calculating scores from AI/teacher results
            # For now, we'll set it to 0
            pass
        
        competition_stats = {
            "total_attempts": attempts_count,
            "average_score": avg_score,
            "questions_count": exam_questions_count,
            "total_marks": exam.total_marks,
            "duration_minutes": exam.total_duration
        }
    
    # Check if user is registered
    user_registration = None
    if user_id:
        user_registration = db.query(EventRegistration).filter(
            EventRegistration.event_id == event_id,
            EventRegistration.user_id == user_id
        ).first()
    
    return {
        "event": EventDetailedOut.model_validate(event),
        "exam": ExamDetailedOut.model_validate(exam) if exam else None,
        "competition_stats": competition_stats,
        "total_registrations": total_registrations,
        "user_registration": user_registration,
        "is_registered": user_registration is not None
    }


def register_for_competition(
    db: Session,
    event_id: uuid.UUID,
    user_id: uuid.UUID,
    registration_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Register user for competition and create exam assignment"""
    
    # Get competition event
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if event.status != EventStatusEnum.PUBLISHED:
        raise HTTPException(status_code=400, detail="Competition is not open for registration")
    
    # Check if user is already registered
    existing_registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if existing_registration:
        raise HTTPException(status_code=400, detail="User already registered for this competition")
    
    # Create event registration (using existing registration logic)
    from Cruds.Events.Events import create_event_registration
    from Schemas.Events.Events import EventRegistrationCreate
    
    registration_schema = EventRegistrationCreate(
        event_id=event_id,
        ticket_id=registration_data.get('ticket_id'),
        quantity=registration_data.get('quantity', 1),
        attendee_info=registration_data.get('attendee_info'),
        special_requirements=registration_data.get('special_requirements'),
        emergency_contact=registration_data.get('emergency_contact')
    )
    
    event_registration = create_event_registration(db, registration_schema, user_id)
    
    # Create exam assignment if exam exists
    if event.competition_exam_id:
        # Check if user already has exam assignment
        existing_assignment = db.query(StudentExamAssignment).filter(
            StudentExamAssignment.exam_id == event.competition_exam_id,
            StudentExamAssignment.student_id == user_id
        ).first()
        
        if not existing_assignment:
            exam_assignment = StudentExamAssignment(
                exam_id=event.competition_exam_id,
                student_id=user_id,
                assigned_at=datetime.now(timezone.utc),
                status='assigned',
                assignment_source='competition',
                source_classroom_id=None
            )
            db.add(exam_assignment)
            db.commit()
    
    return {
        "registration": event_registration,
        "exam_assigned": event.competition_exam_id is not None,
        "message": "Successfully registered for competition"
    }


def get_competition_leaderboard(
    db: Session,
    event_id: uuid.UUID,
    limit: int = 50
) -> List[Dict[str, Any]]:
    """Get competition leaderboard based on exam results"""
    
    # Get competition event
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if not event.competition_exam_id:
        return []
    
    # Get exam attempts with user information
    attempts = db.query(StudentExamAttempt).options(
        joinedload(StudentExamAttempt.student)
    ).filter(
        StudentExamAttempt.exam_id == event.competition_exam_id,
        StudentExamAttempt.status == 'submitted'
    ).all()
    
    leaderboard = []
    for attempt in attempts:
        # Calculate total score from AI and teacher results
        from Models.Exam import StudentExamAIResult, StudentExamTeacherResult
        
        # Get AI scores
        ai_scores = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt.id
        ).all()
        
        # Get teacher scores (if available)
        teacher_scores = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt.id
        ).all()
        
        # Calculate total score (prefer teacher scores over AI scores)
        total_score = 0
        scored_questions = set()
        
        # Add teacher scores first
        for teacher_score in teacher_scores:
            if teacher_score.teacher_score is not None:
                total_score += teacher_score.teacher_score
                scored_questions.add(teacher_score.question_id)
        
        # Add AI scores for questions not scored by teacher
        for ai_score in ai_scores:
            if ai_score.question_id not in scored_questions and ai_score.ai_score is not None:
                total_score += ai_score.ai_score
                scored_questions.add(ai_score.question_id)
        
        # Get user info
        user = attempt.student
        
        leaderboard.append({
            "user_id": str(user.id),
            "username": user.username,
            "full_name": f"{user.first_name} {user.last_name}",
            "total_score": total_score,
            "attempt_id": str(attempt.id),
            "completed_at": attempt.completed_at,
            "duration_seconds": attempt.duration_seconds,
            "questions_answered": len(scored_questions)
        })
    
    # Sort by total score (descending) and completion time (ascending)
    leaderboard.sort(key=lambda x: (-x["total_score"], x["completed_at"] or datetime.max))
    
    # Add rank
    for i, entry in enumerate(leaderboard):
        entry["rank"] = i + 1
    
    return leaderboard[:limit]


def get_user_competition_result(
    db: Session,
    event_id: uuid.UUID,
    user_id: uuid.UUID
) -> Dict[str, Any]:
    """Get user's competition result"""
    
    # Get competition event
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if not event.competition_exam_id:
        raise HTTPException(status_code=400, detail="No exam associated with this competition")
    
    # Get user's exam attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == event.competition_exam_id,
        StudentExamAttempt.student_id == user_id
    ).first()
    
    if not attempt:
        return {
            "has_attempted": False,
            "message": "User has not attempted this competition"
        }
    
    # Get detailed results
    from Models.Exam import StudentExamAIResult, StudentExamTeacherResult
    
    # Get question-wise results
    ai_results = db.query(StudentExamAIResult).filter(
        StudentExamAIResult.attempt_id == attempt.id
    ).all()
    
    teacher_results = db.query(StudentExamTeacherResult).filter(
        StudentExamTeacherResult.attempt_id == attempt.id
    ).all()
    
    # Calculate total score and prepare detailed results
    total_score = 0
    question_results = {}
    
    # Process teacher results first
    for result in teacher_results:
        question_results[str(result.question_id)] = {
            "teacher_score": result.teacher_score,
            "teacher_feedback": result.teacher_feedback,
            "ai_score": None,
            "ai_feedback": None
        }
        if result.teacher_score:
            total_score += result.teacher_score
    
    # Add AI results
    for result in ai_results:
        question_id = str(result.question_id)
        if question_id in question_results:
            question_results[question_id]["ai_score"] = result.ai_score
            question_results[question_id]["ai_feedback"] = result.ai_feedback
        else:
            question_results[question_id] = {
                "teacher_score": None,
                "teacher_feedback": None,
                "ai_score": result.ai_score,
                "ai_feedback": result.ai_feedback
            }
            if result.ai_score:
                total_score += result.ai_score
    
    # Get user's rank in leaderboard
    leaderboard = get_competition_leaderboard(db, event_id, 1000)  # Get full leaderboard
    user_rank = None
    for entry in leaderboard:
        if entry["user_id"] == str(user_id):
            user_rank = entry["rank"]
            break
    
    return {
        "has_attempted": True,
        "attempt_id": str(attempt.id),
        "total_score": total_score,
        "completed_at": attempt.completed_at,
        "duration_seconds": attempt.duration_seconds,
        "rank": user_rank,
        "total_participants": len(leaderboard),
        "question_results": question_results,
        "is_checked": attempt.is_ai_checked or attempt.is_teacher_checked
    }


def get_available_exams_for_competition(db: Session, organizer_id: uuid.UUID) -> List[Dict[str, Any]]:
    """Get exams that can be used for competitions"""
    
    # For now, we'll return all exams
    # In a more sophisticated system, you might want to filter by:
    # - Exams created by the organizer
    # - Exams in specific subjects
    # - Exams with certain characteristics
    
    exams = db.query(Exam).all()
    
    exam_list = []
    for exam in exams:
        exam_list.append({
            "id": str(exam.id),
            "title": exam.title,
            "description": exam.description,
            "total_marks": exam.total_marks,
            "total_duration": exam.total_duration,
            "questions_count": len(exam.questions),
            "created_at": exam.created_at
        })
    
    return exam_list


def start_competition_exam(
    db: Session,
    event_id: uuid.UUID,
    user_id: uuid.UUID
) -> Dict[str, Any]:
    """Start exam for competition participant"""

    # Get competition event
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()

    if not event:
        raise HTTPException(status_code=404, detail="Competition not found")

    if not event.competition_exam_id:
        raise HTTPException(status_code=400, detail="No exam associated with this competition")

    # Check if user is registered for the competition
    registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == user_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).first()

    if not registration:
        raise HTTPException(status_code=403, detail="User not registered for this competition")

    # Check if competition is active
    now = datetime.now(timezone.utc)
    if now < event.start_datetime:
        raise HTTPException(status_code=400, detail="Competition has not started yet")

    if now > event.end_datetime:
        raise HTTPException(status_code=400, detail="Competition has ended")

    # Check if user already has an attempt
    existing_attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == event.competition_exam_id,
        StudentExamAttempt.student_id == user_id
    ).first()

    if existing_attempt:
        if existing_attempt.status == 'submitted':
            raise HTTPException(status_code=400, detail="User has already completed this competition")
        else:
            # Return existing attempt info
            return {
                "attempt_id": str(existing_attempt.id),
                "exam_id": str(event.competition_exam_id),
                "started_at": existing_attempt.started_at,
                "status": "resumed"
            }

    # Create new exam attempt
    attempt = StudentExamAttempt(
        id=uuid.uuid4(),
        exam_id=event.competition_exam_id,
        student_id=user_id,
        started_at=datetime.now(timezone.utc),
        status='started'
    )

    db.add(attempt)
    db.commit()
    db.refresh(attempt)

    return {
        "attempt_id": str(attempt.id),
        "exam_id": str(event.competition_exam_id),
        "started_at": attempt.started_at,
        "status": "started"
    }


def get_competition_exam_questions(
    db: Session,
    event_id: uuid.UUID,
    user_id: uuid.UUID
) -> List[Dict[str, Any]]:
    """Get exam questions for competition participant"""

    # Verify user has access to this competition
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()

    if not event or not event.competition_exam_id:
        raise HTTPException(status_code=404, detail="Competition exam not found")

    # Check user registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == user_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).first()

    if not registration:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get exam and questions
    exam = db.query(Exam).filter(Exam.id == event.competition_exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Return questions (this would typically be handled by the existing exam session system)
    questions = []
    for question in exam.questions:
        question_data = {
            "id": str(question.id),
            "text": question.text,
            "type": question.Type.value,
            "marks": question.marks,
            "image_url": question.imageUrl
        }

        # Add options for MCQ questions
        if question.Type.value == "mcq" and question.options:
            question_data["options"] = [
                {
                    "id": str(option.id),
                    "text": option.option_text
                }
                for option in question.options
            ]

        questions.append(question_data)

    return questions


def submit_competition_exam(
    db: Session,
    event_id: uuid.UUID,
    user_id: uuid.UUID,
    answers: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Submit exam answers for competition"""

    # Get competition and exam
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True
    ).first()

    if not event or not event.competition_exam_id:
        raise HTTPException(status_code=404, detail="Competition exam not found")

    # Get user's attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == event.competition_exam_id,
        StudentExamAttempt.student_id == user_id
    ).first()

    if not attempt:
        raise HTTPException(status_code=404, detail="No exam attempt found")

    if attempt.status == 'submitted':
        raise HTTPException(status_code=400, detail="Exam already submitted")

    # Save answers
    for answer_data in answers:
        # Check if answer already exists
        existing_answer = db.query(StudentExamAnswer).filter(
            StudentExamAnswer.attempt_id == attempt.id,
            StudentExamAnswer.question_id == answer_data['question_id']
        ).first()

        if existing_answer:
            existing_answer.answer = answer_data['answer']
            existing_answer.submitted_at = datetime.now(timezone.utc)
        else:
            answer = StudentExamAnswer(
                attempt_id=attempt.id,
                question_id=answer_data['question_id'],
                answer=answer_data['answer'],
                submitted_at=datetime.now(timezone.utc)
            )
            db.add(answer)

    # Update attempt status
    attempt.status = 'submitted'
    attempt.completed_at = datetime.now(timezone.utc)
    attempt.duration_seconds = int((attempt.completed_at - attempt.started_at).total_seconds())

    db.commit()

    return {
        "attempt_id": str(attempt.id),
        "submitted_at": attempt.completed_at,
        "duration_seconds": attempt.duration_seconds,
        "status": "submitted"
    }
