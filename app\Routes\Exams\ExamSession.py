from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Query
import uuid
import asyncio
from sqlalchemy.orm import Session
from config.session import get_db
from datetime import datetime, timedelta, timezone
import json
from config.redis import get_redis
from config.mongodb import get_mongodb
from Models.users import User
from Models.Exam import StudentExamAttempt, StudentExamAnswer
from config.deps import oauth2_scheme
from config.permission import require_type
from Schemas.Exams.ExamSession import (
    StartSessionRequest, StartSessionResponse, SubmitSessionRequest, SubmitSessionResponse,
    ReconnectionRequest, ReconnectionRequestResponse, TeacherApprovalRequest, TeacherApprovalResponse,
    ReconnectionStatusResponse, SessionResumeResponse, PendingReconnectionRequest
)
from Cruds.Exams.ExamSession import ExamSessionCRUD
from typing import List

router = APIRouter()



# --- Endpoint: Start Exam Session ---
@router.post("/exam-session/start", response_model=StartSessionResponse)
async def start_exam_session(
    req: StartSessionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Start a new exam session for a student.

    **Authentication:** Requires student role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)
    session_id = await crud.start_session(db, str(current_user.id), req.exam_id, mongo_db)
    return StartSessionResponse(session_id=session_id)

# --- Endpoint: Final Submission ---
@router.post("/exam-session/submit", response_model=SubmitSessionResponse)
async def submit_exam_session(
    req: SubmitSessionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Submit an exam session with attempted answers.

    **Required Fields:**
    - exam: Complete exam object with all details
    - questions: List of all exam questions (cannot be empty)
    - student_answers: List of attempted answers (cannot be empty)

    **Authentication:** Requires student role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)

    # Extract and validate data from request
    exam_data = req.exam.model_dump() if req.exam else None
    questions_data = [q.model_dump() for q in req.questions] if req.questions else None
    student_answers = [a.model_dump() for a in req.student_answers] if req.student_answers else None

    result = await crud.submit_session(
        db,
        req.session_id,
        str(current_user.id),
        exam_data,
        questions_data,
        student_answers,
        mongo_db
    )
    return SubmitSessionResponse(**result)

# --- Endpoint: Auto-Submit for Disqualified Students ---
@router.post("/exam-session/auto-submit", response_model=SubmitSessionResponse)
async def auto_submit_disqualified_session(
    req: SubmitSessionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Auto-submit an exam session for disqualified students (e.g., cheating detection).

    This endpoint allows submission with empty answers for students who were
    automatically disqualified during the exam.

    **Required Fields:**
    - exam: Complete exam object with all details
    - questions: List of all exam questions (cannot be empty)
    - student_answers: Can be empty for disqualified students

    **Authentication:** Requires student role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)

    # Extract and validate data from request
    exam_data = req.exam.model_dump() if req.exam else None
    questions_data = [q.model_dump() for q in req.questions] if req.questions else None
    student_answers = [a.model_dump() for a in req.student_answers] if req.student_answers else []

    # Mark session as disqualified before submission
    session_key = f"exam_session:{req.session_id}"
    await redis.hset(session_key, "status", "disqualified")

    # Log the disqualification event for security audit
    from config.logging import log_security_event
    log_security_event(
        event_type="STUDENT_DISQUALIFIED",
        message=f"Student auto-submitted due to disqualification",
        user_id=str(current_user.id),
        additional_data={
            "session_id": req.session_id,
            "exam_id": exam_data.get('exam_id') if exam_data else None,
            "reason": "cheating_detection",
            "answers_count": len(student_answers)
        }
    )

    result = await crud.submit_session(
        db,
        req.session_id,
        str(current_user.id),
        exam_data,
        questions_data,
        student_answers,
        mongo_db
    )
    return SubmitSessionResponse(**result)

# --- Student Reconnection Request ---
@router.post("/exam-session/{session_id}/request-reconnection", response_model=ReconnectionRequestResponse)
async def request_reconnection(
    session_id: str,
    req: ReconnectionRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Request reconnection to an exam session after disconnection.

    **Authentication:** Requires student role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)
    request_id = await crud.request_reconnection(db, session_id, str(current_user.id), req.reason, mongo_db)
    return ReconnectionRequestResponse(request_id=request_id, status="pending_approval")

# --- Teacher View Pending Requests ---
@router.get("/admin/reconnection-requests", response_model=List[PendingReconnectionRequest])
async def get_pending_reconnection_requests(
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    teacher: User = Depends(require_type("teacher"))
):
    """
    Get pending reconnection requests for teacher's exams.

    **Authentication:** Requires teacher role authentication.
    """
    crud = ExamSessionCRUD(redis)
    requests = await crud.get_pending_reconnection_requests(db, str(teacher.id))
    return [PendingReconnectionRequest(**req) for req in requests]

# --- Teacher Approve/Deny Request ---
@router.post("/admin/reconnection-request/{request_id}/approve", response_model=TeacherApprovalResponse)
async def approve_reconnection_request(
    request_id: str,
    req: TeacherApprovalRequest,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    teacher: User = Depends(require_type("teacher"))
):
    """
    Approve or deny a student's reconnection request.

    **Authentication:** Requires teacher role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)
    result = await crud.approve_reconnection_request(db, request_id, str(teacher.id), req.approved, req.reason or "", mongo_db)
    await crud.log_admin_action(mongo_db, str(teacher.id), "approve_reconnection", request_id, req.reason or "")
    return TeacherApprovalResponse(**result)

# --- Student Check Reconnection Status ---
@router.get("/exam-session/reconnection-status/{request_id}", response_model=ReconnectionStatusResponse)
async def check_reconnection_status(
    request_id: str,
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Check the status of a reconnection request.

    **Authentication:** Requires student role authentication.
    """
    request_key = f"reconnection_request:{request_id}"
    request = await redis.hgetall(request_key)
    if not request:
        raise HTTPException(status_code=404, detail="Reconnection request not found.")

    if request.get("student_id") != str(current_user.id):
        raise HTTPException(status_code=403, detail="You can only check your own requests.")

    return ReconnectionStatusResponse(
        status=request.get("status"),
        teacher_reason=request.get("teacher_reason"),
        session_id=request.get("session_id") if request.get("status") == "approved" else None
    )

# --- Student Resume Session After Approval ---
@router.get("/exam-session/{session_id}/resume", response_model=SessionResumeResponse)
async def resume_exam_session(
    session_id: str,
    db: Session = Depends(get_db),
    redis=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Resume an exam session after reconnection approval.

    **Authentication:** Requires student role authentication.
    """
    crud = ExamSessionCRUD(redis)
    return await crud.get_session_resume_data(db, session_id, str(current_user.id))

# --- WebSocket Endpoint ---
@router.websocket("/ws/exam-session/{session_id}")
async def exam_session_ws(
    websocket: WebSocket,
    session_id: str,
    redis=Depends(get_redis),
    db: Session = Depends(get_db)
):
    await websocket.accept()
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)

    if not session:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    # Check if exam is already submitted or disqualified
    session_status = session.get("status", "active")
    if session_status in ["submitted", "disqualified", "ended"]:
        await websocket.send_json({
            "type": "session_closed",
            "reason": f"Exam session is {session_status}",
            "message": "This exam has already been completed and cannot be re-entered."
        })
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    # Also check database for completed attempts
    exam_id = session.get("exam_id")
    student_id = session.get("student_id")
    if exam_id and student_id:
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()

        if attempt:
            await websocket.send_json({
                "type": "session_closed",
                "reason": "exam_already_submitted",
                "message": "This exam has already been submitted and cannot be re-entered.",
                "completed_at": attempt.completed_at.isoformat() if attempt.completed_at else None
            })
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

    # Note: Authentication should be implemented via query parameters or message-based auth
    # For now, we'll allow connections but validate session ownership via messages
    
    # Check if session is active
    if session.get("status") != "active":
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    # Send current session state to student immediately upon connection
    current_answers = json.loads(session.get("answers", "{}"))
    start_time = datetime.fromisoformat(session["start_time"])
    # Ensure start_time is timezone-aware
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=timezone.utc)

    duration_seconds = int(session["duration"])
    end_time = start_time + timedelta(seconds=duration_seconds)
    current_time = datetime.now(timezone.utc)
    remaining_seconds = max(0, int((end_time - current_time).total_seconds()))
    
    # Send initial state
    await websocket.send_json({
        "type": "session_resume",
        "current_answers": current_answers,
        "remaining_time_seconds": remaining_seconds,
        "strikes": int(session.get("strikes", 0)),
        "session_id": session_id
    })
    
    strikes = int(session.get("strikes", 0))
    last_heartbeat = datetime.now(timezone.utc)
    missed_heartbeats = 0
    
    try:
        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=40)  # 2 missed heartbeats = 40s
                msg = json.loads(data)
                
                if msg.get("type") == "heartbeat":
                    last_heartbeat = datetime.now(timezone.utc)
                    await redis.hset(session_key, "last_heartbeat", last_heartbeat.isoformat())
                    missed_heartbeats = 0
                    
                elif msg.get("type") == "cheat":
                    strikes = int(await redis.hincrby(session_key, "strikes", 1))
                    if strikes >= 3:
                        await redis.hset(session_key, mapping={"status": "disqualified"})
                        await websocket.send_json({"event": "disqualified", "reason": "Cheating detected."})
                        await websocket.close()
                        return
                        
                elif "answers" in msg:
                    # Sync answers
                    await redis.hset(session_key, "answers", json.dumps(msg["answers"]))
                    # Send confirmation
                    await websocket.send_json({
                        "type": "answers_saved",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                    
                # else: ignore unknown
                
            except asyncio.TimeoutError:
                missed_heartbeats += 1
                if missed_heartbeats >= 2:
                    await redis.hset(session_key, mapping={"status": "disconnected"})
                    await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
                    return
                    
    except WebSocketDisconnect:
        await redis.hset(session_key, mapping={"status": "disconnected"})
        return

# --- ADMIN ENDPOINTS ---

@router.get("/admin/exam-session/{session_id}")
async def admin_view_session(
    session_id: str,
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    """
    View exam session details for admin monitoring.

    **Authentication:** Requires admin role authentication.
    """
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "view", session_id, "")
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")
    return session

@router.post("/admin/exam-session/{session_id}/submit")
async def admin_force_submit(
    session_id: str,
    reason: str = Query(..., description="Reason for force submission"),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    """
    Force submit an exam session (admin only).

    **Authentication:** Requires admin role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "force_submit", session_id, reason)
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")

    # Save answers and disqualification reason to PostgreSQL
    answers = json.loads(session.get("answers", "{}"))
    status_val = session.get("status", "ended")
    disqualification_reason = reason if status_val == "disqualified" else None
    # Ensure UUIDs are properly converted
    exam_id = session["exam_id"]
    student_id = session["student_id"]

    # Convert to UUID if they're strings
    if isinstance(exam_id, str):
        exam_id = uuid.UUID(exam_id)
    if isinstance(student_id, str):
        student_id = uuid.UUID(student_id)

    attempt = StudentExamAttempt(
        id=uuid.uuid4(),
        exam_id=exam_id,
        student_id=student_id,
        started_at=datetime.now(timezone.utc),
        completed_at=datetime.now(timezone.utc),
        is_teacher_checked=False,
        is_ai_checked=False,
    )
    if disqualification_reason:
        attempt.status = "disqualified"
    db.add(attempt)
    db.commit()
    db.refresh(attempt)
    # Save answers with comprehensive data (same as regular submission)
    current_time = datetime.now(timezone.utc)

    for qid, ans in answers.items():
        # Convert question_id to UUID if it's a string
        question_id = uuid.UUID(qid) if isinstance(qid, str) else qid

        # Handle both simple string answers and complex answer objects
        if isinstance(ans, dict):
            answer_text = ans.get('answer', ans.get('text', ''))
            time_spent = ans.get('time_spent_seconds', None)
        else:
            answer_text = str(ans) if ans is not None else ''
            time_spent = None

        student_answer = StudentExamAnswer(
            attempt_id=attempt.id,
            question_id=question_id,
            answer=answer_text,  # Main answer field
            answer_text=answer_text,  # Compatibility field
            submitted_at=current_time,
            time_spent_seconds=time_spent
        )
        db.add(student_answer)

    db.commit()
    await redis.delete(session_key)
    return {"success": True, "admin_forced": True, "disqualification_reason": disqualification_reason}

@router.post("/admin/exam-session/{session_id}/terminate")
async def admin_terminate_session(
    session_id: str,
    reason: str = Query(..., description="Reason for termination"),
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    """
    Terminate an exam session (admin only).

    **Authentication:** Requires admin role authentication.
    """
    session_key = f"exam_session:{session_id}"
    session = await redis.hgetall(session_key)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found.")
    await redis.hset(session_key, mapping={"status": "terminated_by_admin"})
    crud = ExamSessionCRUD(redis, mongo_db)
    await crud.log_admin_action(mongo_db, str(admin.id), "terminate", session_id, reason)
    return {"success": True, "terminated": True, "reason": reason}

@router.get("/admin/exam-sessions/active")
async def admin_list_active_sessions(
    redis=Depends(get_redis),
    mongo_db=Depends(get_mongodb),
    token: str = Depends(oauth2_scheme),
    admin: User = Depends(require_type("admin")),
):
    """
    List all active exam sessions (admin only).

    **Authentication:** Requires admin role authentication.
    """
    crud = ExamSessionCRUD(redis, mongo_db)
    sessions = await crud.get_active_sessions()
    await crud.log_admin_action(mongo_db, str(admin.id), "list_active", "*", "")
    return sessions