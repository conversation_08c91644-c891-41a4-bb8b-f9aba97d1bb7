from pydantic import BaseModel, Field, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class InstituteTypeEnum(str, Enum):
    UNIVERSITY = "university"
    COLLEGE = "college"
    SCHOOL = "school"
    TRAINING_CENTER = "training_center"
    RESEARCH_INSTITUTE = "research_institute"
    VOCATIONAL_SCHOOL = "vocational_school"
    ONLINE_PLATFORM = "online_platform"
    OTHER = "other"


class VerificationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


# Base Schemas
class InstituteRegistrationBase(BaseModel):
    # User account details
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="Institute email address")
    mobile: str = Field(..., description="Institute contact number")
    password: str = Field(..., min_length=8, description="Account password")
    country: str = Field(..., description="Country")
    
    # Institute profile details
    institute_name: str = Field(..., min_length=2, max_length=200, description="Official institute name")
    description: Optional[str] = Field(None, max_length=2000, description="Institute description")
    address: Optional[str] = Field(None, description="Physical address")
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    
    # Institute details
    website: Optional[str] = Field(None, description="Official website URL")
    phone: Optional[str] = Field(None, description="Official phone number")
    institute_email: Optional[EmailStr] = Field(None, description="Official institute email")
    established_year: Optional[int] = Field(None, ge=1800, le=2024, description="Year established")
    institute_type: InstituteTypeEnum = Field(..., description="Type of institute")
    accreditation: Optional[str] = Field(None, description="Accreditation details")
    
    # Social media
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile URL")
    facebook_url: Optional[str] = Field(None, description="Facebook page URL")
    twitter_url: Optional[str] = Field(None, description="Twitter profile URL")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower()

    @validator('website', 'linkedin_url', 'facebook_url', 'twitter_url')
    def validate_urls(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            return f'https://{v}'
        return v


class InstituteProfileUpdate(BaseModel):
    institute_name: Optional[str] = Field(None, min_length=2, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    website: Optional[str] = None
    phone: Optional[str] = None
    institute_email: Optional[EmailStr] = None
    established_year: Optional[int] = Field(None, ge=1800, le=2024)
    institute_type: Optional[InstituteTypeEnum] = None
    accreditation: Optional[str] = None
    linkedin_url: Optional[str] = None
    facebook_url: Optional[str] = None
    twitter_url: Optional[str] = None
    logo_url: Optional[str] = None
    banner_url: Optional[str] = None


class InstituteVerificationUpdate(BaseModel):
    verification_status: VerificationStatusEnum = Field(..., description="New verification status")
    verification_notes: Optional[str] = Field(None, max_length=1000, description="Admin notes")


# Output Schemas
class InstituteProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    institute_name: str
    description: Optional[str]
    address: Optional[str]
    city: Optional[str]
    state: Optional[str]
    country: Optional[str]
    postal_code: Optional[str]
    website: Optional[str]
    phone: Optional[str]
    email: Optional[str]
    established_year: Optional[int]
    institute_type: Optional[str]
    accreditation: Optional[str]
    is_verified: bool
    verification_status: str
    verification_notes: Optional[str]
    verified_at: Optional[datetime]
    linkedin_url: Optional[str]
    facebook_url: Optional[str]
    twitter_url: Optional[str]
    logo_url: Optional[str]
    banner_url: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InstituteUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    institute_profile: Optional[InstituteProfileOut]

    class Config:
        from_attributes = True


class InstituteDetailedOut(BaseModel):
    user: InstituteUserOut
    profile: InstituteProfileOut
    total_competitions: int = 0
    total_mentors: int = 0
    active_competitions: int = 0
    verification_status: str

    class Config:
        from_attributes = True


class InstituteListOut(BaseModel):
    id: UUID
    username: str
    institute_name: str
    city: Optional[str]
    state: Optional[str]
    country: Optional[str]
    institute_type: Optional[str]
    is_verified: bool
    verification_status: str
    logo_url: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class InstituteListResponse(BaseModel):
    institutes: List[InstituteListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


class InstituteStatsOut(BaseModel):
    total_institutes: int
    verified_institutes: int
    pending_verification: int
    rejected_institutes: int
    institutes_by_type: Dict[str, int]
    institutes_by_country: Dict[str, int]
    recent_registrations: int  # Last 30 days


# Admin verification schemas
class InstituteVerificationListOut(BaseModel):
    id: UUID
    username: str
    institute_name: str
    institute_type: Optional[str]
    country: Optional[str]
    verification_status: str
    created_at: datetime
    submitted_documents: List[str] = []

    class Config:
        from_attributes = True


class InstituteVerificationDetailOut(BaseModel):
    institute: InstituteDetailedOut
    submitted_documents: List[Dict[str, Any]] = []
    verification_history: List[Dict[str, Any]] = []

    class Config:
        from_attributes = True


# Search and filter schemas
class InstituteSearchFilter(BaseModel):
    search: Optional[str] = None
    institute_type: Optional[InstituteTypeEnum] = None
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    verification_status: Optional[VerificationStatusEnum] = None
    is_verified: Optional[bool] = None
    established_year_from: Optional[int] = None
    established_year_to: Optional[int] = None


# Document upload schemas
class InstituteDocumentUpload(BaseModel):
    document_type: str = Field(..., description="Type of document")
    document_url: str = Field(..., description="URL of uploaded document")
    document_name: str = Field(..., description="Name of the document")
    description: Optional[str] = Field(None, description="Document description")


class InstituteDocumentOut(BaseModel):
    id: UUID
    institute_id: UUID
    document_type: str
    document_url: str
    document_name: str
    description: Optional[str]
    uploaded_at: datetime
    verified: bool = False
    verified_at: Optional[datetime]
    verified_by: Optional[UUID]

    class Config:
        from_attributes = True
