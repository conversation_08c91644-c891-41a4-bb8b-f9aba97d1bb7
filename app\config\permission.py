from fastapi import Depends, HTTPException, status
from .deps import get_current_user
from sqlalchemy.orm import Session
from .session import get_db
from Models.users import User

def require_type(required_type: str):
    def type_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        if user.user_type.value not in (required_type, "admin"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User '{user.username}' does not have permission for: '{required_type}'",
            )
        return user
    return type_dependency
