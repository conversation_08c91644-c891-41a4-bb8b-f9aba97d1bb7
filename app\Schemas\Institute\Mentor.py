from pydantic import BaseModel, Field, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class MentorVerificationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


class AssociationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    ACTIVE = "active"
    INACTIVE = "inactive"


class AssociationTypeEnum(str, Enum):
    MENTOR_APPLIED = "mentor_applied"
    INSTITUTE_INVITED = "institute_invited"


# Base Schemas
class MentorRegistrationBase(BaseModel):
    # User account details
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="Mentor email address")
    mobile: str = Field(..., description="Mentor contact number")
    password: str = Field(..., min_length=8, description="Account password")
    country: str = Field(..., description="Country")
    
    # Essential mentor profile details
    bio: Optional[str] = Field(None, max_length=2000, description="Professional bio")
    experience_years: Optional[int] = Field(None, ge=0, le=50, description="Years of experience")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Hourly rate in USD")

    # Subject relationships (UUIDs)
    expertise_subject_ids: List[UUID] = Field(default=[], description="Subject IDs for expertise areas")
    preferred_subject_ids: List[UUID] = Field(default=[], description="Subject IDs for preferred subjects")

    # Languages and availability
    languages: Optional[List[str]] = Field(None, description="Languages spoken")
    availability_hours: Optional[Dict[str, Any]] = Field(None, description="Available hours schedule")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower()


class MentorProfileUpdate(BaseModel):
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)

    # Subject relationships (UUIDs)
    expertise_subject_ids: Optional[List[UUID]] = None
    preferred_subject_ids: Optional[List[UUID]] = None

    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None
    profile_image_url: Optional[str] = None


class MentorVerificationUpdate(BaseModel):
    verification_status: MentorVerificationStatusEnum = Field(..., description="New verification status")
    verification_notes: Optional[str] = Field(None, max_length=1000, description="Admin notes")


# Institute-Mentor Association Schemas
class MentorApplicationCreate(BaseModel):
    institute_id: UUID = Field(..., description="Institute to apply to")
    application_message: str = Field(..., min_length=10, max_length=1000, description="Application message")
    proposed_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Proposed hourly rate")


class InstituteInvitationCreate(BaseModel):
    mentor_id: UUID = Field(..., description="Mentor to invite")
    invitation_message: str = Field(..., min_length=10, max_length=1000, description="Invitation message")
    offered_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Offered hourly rate")
    special_terms: Optional[str] = Field(None, max_length=500, description="Special terms")


class AssociationResponseCreate(BaseModel):
    response_message: str = Field(..., min_length=10, max_length=1000, description="Response message")
    accept: bool = Field(..., description="Accept or reject the application/invitation")
    negotiated_rate: Optional[Decimal] = Field(None, ge=0, description="Negotiated hourly rate")
    contract_terms: Optional[str] = Field(None, max_length=1000, description="Contract terms")


# Output Schemas
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    availability_hours: Optional[Dict[str, Any]]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata

    # Subject relationships (will be populated from relationships)
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    mentor_profile: Optional[MentorProfileOut]

    class Config:
        from_attributes = True


class MentorDetailedOut(BaseModel):
    user: MentorUserOut
    profile: MentorProfileOut
    total_competitions: int = 0
    active_institutes: int = 0
    average_rating: Optional[Decimal] = None
    verification_status: str

    class Config:
        from_attributes = True


class MentorListOut(BaseModel):
    id: UUID
    username: str
    full_name: str
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    current_position: Optional[str]
    hourly_rate: Optional[Decimal]
    rating: Optional[Decimal]
    is_verified: bool
    verification_status: str
    profile_image_url: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class MentorListResponse(BaseModel):
    mentors: List[MentorListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# Association Output Schemas
class MentorInstituteAssociationOut(BaseModel):
    id: UUID
    mentor_id: UUID
    institute_id: UUID
    status: str
    association_type: str
    application_message: Optional[str]
    response_message: Optional[str]
    applied_at: datetime
    responded_at: Optional[datetime]
    approved_at: Optional[datetime]
    hourly_rate: Optional[Decimal]
    contract_terms: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]

    class Config:
        from_attributes = True


class MentorInstituteAssociationDetailedOut(BaseModel):
    association: MentorInstituteAssociationOut
    mentor: MentorListOut
    institute: Optional[Dict[str, Any]] = None  # Institute details as dict to avoid circular import

    class Config:
        from_attributes = True


# Search and filter schemas
class MentorSearchFilter(BaseModel):
    search: Optional[str] = None
    country: Optional[str] = None
    is_verified: Optional[bool] = None


class AssociationSearchFilter(BaseModel):
    status: Optional[AssociationStatusEnum] = None
    association_type: Optional[AssociationTypeEnum] = None
    institute_id: Optional[UUID] = None
    mentor_id: Optional[UUID] = None


# Statistics schemas
class MentorStatsOut(BaseModel):
    total_mentors: int
    verified_mentors: int
    pending_verification: int
    active_associations: int
    mentors_by_expertise: Dict[str, int]
    average_hourly_rate: Optional[Decimal]
    recent_registrations: int  # Last 30 days


class InstituteAssociationStatsOut(BaseModel):
    total_associations: int
    pending_applications: int
    active_mentors: int
    average_mentor_rating: Optional[Decimal]
    total_competitions_checked: int
