"""
Local File Storage Service

This module provides comprehensive file storage functionality for the EduFair application,
including file uploads, validation, security checks, and file management utilities.
"""

import os
import uuid
import shutil
import base64
import gzip
import zipfile
import io
from pathlib import Path
from typing import Optional, Tuple, List, Dict
from fastapi import UploadFile, HTTPException
from PIL import Image, ImageOps
import hashlib

# Conditional import for magic (libmagic)
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    print("Warning: python-magic not available. File type detection will use basic methods.")
from datetime import datetime

from config.config import settings
from config.logging import get_logger, log_security_event

logger = get_logger(__name__)

class FileStorageService:
    """Service for handling local file storage operations"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.profile_pictures_dir = self.upload_dir / settings.PROFILE_PICTURES_DIR
        self.task_attachments_dir = self.upload_dir / settings.TASK_ATTACHMENTS_DIR
        self.compressed_dir = self.upload_dir / settings.COMPRESSED_DIR

        # Create directories if they don't exist
        self._ensure_directories()
    
    def _ensure_directories(self) -> None:
        """Create upload directories if they don't exist"""
        try:
            self.upload_dir.mkdir(exist_ok=True)
            self.profile_pictures_dir.mkdir(exist_ok=True)
            self.task_attachments_dir.mkdir(exist_ok=True)
            self.compressed_dir.mkdir(exist_ok=True)

            # Create subdirectories for organization
            (self.profile_pictures_dir / "thumbnails").mkdir(exist_ok=True)
            (self.task_attachments_dir / "documents").mkdir(exist_ok=True)
            (self.task_attachments_dir / "images").mkdir(exist_ok=True)
            
            logger.info("File storage directories initialized successfully")
        except Exception as e:
            logger.error(f"Failed to create upload directories: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to initialize file storage")
    
    def _validate_file(self, file: UploadFile, allowed_extensions: set) -> None:
        """Validate uploaded file for security and format compliance"""
        
        # Check file size
        if hasattr(file, 'size') and file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Check file extension
        if file.filename:
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in allowed_extensions:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file type. Allowed: {', '.join(allowed_extensions)}"
                )
        else:
            raise HTTPException(status_code=400, detail="Filename is required")
        
        # Read file content for validation
        file_content = file.file.read()
        file.file.seek(0)  # Reset file pointer
        
        # Check actual file size
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Validate file content using python-magic (if available)
        try:
            if MAGIC_AVAILABLE:
                file_mime = magic.from_buffer(file_content, mime=True)
            else:
                # Fallback to basic file extension detection
                file_extension = Path(file.filename).suffix.lower()
                extension_to_mime = {
                    '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
                    '.png': 'image/png', '.gif': 'image/gif',
                    '.webp': 'image/webp', '.pdf': 'application/pdf',
                    '.doc': 'application/msword',
                    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    '.txt': 'text/plain', '.mp4': 'video/mp4',
                    '.avi': 'video/x-msvideo', '.mov': 'video/quicktime'
                }
                file_mime = extension_to_mime.get(file_extension, 'application/octet-stream')
            
            # Define allowed MIME types
            allowed_mimes = {
                # Images
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                # Documents
                'application/pdf', 'text/plain', 'text/rtf',
                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.oasis.opendocument.text'
            }
            
            if file_mime not in allowed_mimes:
                log_security_event(
                    event_type="SUSPICIOUS_FILE_UPLOAD",
                    message=f"Attempted upload of suspicious file type: {file_mime}",
                    additional_data={"original_filename": file.filename, "mime_type": file_mime}
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file content. Detected type: {file_mime}"
                )
                
        except Exception as e:
            logger.error(f"File validation error: {str(e)}")
            raise HTTPException(status_code=400, detail="File validation failed")
    
    def _generate_unique_filename(self, original_filename: str, user_id: str) -> str:
        """Generate a unique filename to prevent conflicts and enhance security"""
        file_ext = Path(original_filename).suffix.lower()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        user_hash = hashlib.md5(user_id.encode()).hexdigest()[:8]
        
        return f"{user_hash}_{timestamp}_{unique_id}{file_ext}"
    
    def _create_thumbnail(self, image_path: Path, thumbnail_path: Path, size: Tuple[int, int] = (150, 150)) -> None:
        """Create a thumbnail for profile pictures"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary (for PNG with transparency)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Create thumbnail
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                
                logger.info(f"Thumbnail created: {thumbnail_path}")
        except Exception as e:
            logger.error(f"Failed to create thumbnail: {str(e)}")
            # Don't raise exception - thumbnail creation is optional
    
    async def save_profile_picture(self, file: UploadFile, user_id: str) -> Tuple[str, str]:
        """
        Save a profile picture and create a thumbnail
        
        Returns:
            Tuple[str, str]: (file_path, thumbnail_path)
        """
        # Validate file
        self._validate_file(file, settings.ALLOWED_IMAGE_EXTENSIONS)
        
        # Generate unique filename
        unique_filename = self._generate_unique_filename(file.filename, user_id)
        file_path = self.profile_pictures_dir / unique_filename
        thumbnail_path = self.profile_pictures_dir / "thumbnails" / unique_filename
        
        try:
            # Save original file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Create thumbnail
            self._create_thumbnail(file_path, thumbnail_path)
            
            # Log successful upload
            logger.info(f"Profile picture saved: {file_path}")
            log_security_event(
                event_type="PROFILE_PICTURE_UPLOADED",
                message=f"User uploaded profile picture",
                user_id=user_id,
                additional_data={"original_filename": file.filename, "saved_as": unique_filename}
            )
            
            # Compress the original file if needed
            compressed_path = self.compress_file(f"{settings.PROFILE_PICTURES_DIR}/{unique_filename}")

            # Return relative paths for database storage
            relative_path = f"{settings.PROFILE_PICTURES_DIR}/{unique_filename}"
            thumbnail_relative_path = f"{settings.PROFILE_PICTURES_DIR}/thumbnails/{unique_filename}"

            if compressed_path and compressed_path != relative_path:
                logger.info(f"Profile picture compressed: {compressed_path}")

            return relative_path, thumbnail_relative_path
            
        except Exception as e:
            # Clean up on failure
            if file_path.exists():
                file_path.unlink()
            if thumbnail_path.exists():
                thumbnail_path.unlink()
            
            logger.error(f"Failed to save profile picture: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to save profile picture")
    
    async def save_task_attachment(self, file: UploadFile, user_id: str, task_id: str) -> str:
        """
        Save a task attachment file
        
        Returns:
            str: file_path relative to upload directory
        """
        # Validate file
        self._validate_file(file, settings.ALLOWED_EXTENSIONS)
        
        # Generate unique filename
        unique_filename = self._generate_unique_filename(file.filename, user_id)
        
        # Determine subdirectory based on file type
        file_ext = Path(file.filename).suffix.lower()
        if file_ext in settings.ALLOWED_IMAGE_EXTENSIONS:
            subdir = "images"
        else:
            subdir = "documents"
        
        file_path = self.task_attachments_dir / subdir / unique_filename
        
        try:
            # Ensure subdirectory exists
            file_path.parent.mkdir(exist_ok=True)
            
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Log successful upload
            logger.info(f"Task attachment saved: {file_path}")
            log_security_event(
                event_type="TASK_ATTACHMENT_UPLOADED",
                message=f"User uploaded task attachment",
                user_id=user_id,
                additional_data={
                    "original_filename": file.filename,
                    "saved_as": unique_filename,
                    "task_id": task_id
                }
            )
            
            # Compress the file if needed
            relative_path = f"{settings.TASK_ATTACHMENTS_DIR}/{subdir}/{unique_filename}"
            compressed_path = self.compress_file(relative_path)

            if compressed_path and compressed_path != relative_path:
                logger.info(f"Task attachment compressed: {compressed_path}")

            # Return relative path for database storage
            return relative_path
            
        except Exception as e:
            # Clean up on failure
            if file_path.exists():
                file_path.unlink()
            
            logger.error(f"Failed to save task attachment: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to save task attachment")
    
    def delete_file(self, relative_path: str) -> bool:
        """
        Delete a file from storage
        
        Args:
            relative_path: Path relative to upload directory
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            full_path = self.upload_dir / relative_path
            if full_path.exists() and full_path.is_file():
                full_path.unlink()
                logger.info(f"File deleted: {full_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {full_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete file {relative_path}: {str(e)}")
            return False
    
    def get_file_info(self, relative_path: str) -> Optional[dict]:
        """Get information about a stored file"""
        try:
            full_path = self.upload_dir / relative_path
            if full_path.exists() and full_path.is_file():
                stat = full_path.stat()
                return {
                    "path": str(full_path),
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_mtime),  # Using mtime instead of deprecated ctime
                    "modified": datetime.fromtimestamp(stat.st_mtime),
                    "exists": True
                }
            return {"exists": False}
        except Exception as e:
            logger.error(f"Failed to get file info for {relative_path}: {str(e)}")
            return {"exists": False, "error": str(e)}

    def read_image_as_base64(self, relative_path: str) -> Optional[Dict[str, str]]:
        """
        Read an image file and convert it to base64 format

        Args:
            relative_path: Path to the image file relative to upload directory

        Returns:
            Dict with base64 data and metadata, or None if file doesn't exist
        """
        try:
            full_path = self.upload_dir / relative_path
            if not full_path.exists() or not full_path.is_file():
                return None

            # Read file as binary
            with open(full_path, "rb") as image_file:
                image_data = image_file.read()

            # Convert to base64
            base64_data = base64.b64encode(image_data).decode('utf-8')

            # Determine MIME type
            if MAGIC_AVAILABLE:
                mime_type = magic.from_file(str(full_path), mime=True)
            else:
                # Fallback to basic file extension detection
                file_extension = full_path.suffix.lower()
                extension_to_mime = {
                    '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
                    '.png': 'image/png', '.gif': 'image/gif',
                    '.webp': 'image/webp', '.pdf': 'application/pdf'
                }
                mime_type = extension_to_mime.get(file_extension, 'application/octet-stream')

            # Get file info
            stat = full_path.stat()

            return {
                "base64_data": base64_data,
                "mime_type": mime_type,
                "size_bytes": stat.st_size,
                "data_url": f"data:{mime_type};base64,{base64_data}"
            }

        except Exception as e:
            logger.error(f"Failed to read image as base64 for {relative_path}: {str(e)}")
            return None

    def get_profile_picture_data(self, relative_path: str) -> Optional[Dict[str, any]]:
        """
        Get profile picture data including both full image and thumbnail as base64

        Args:
            relative_path: Path to the profile picture relative to upload directory

        Returns:
            Dict with full image and thumbnail base64 data, or None if not found
        """
        try:
            if not relative_path:
                return None

            # Get full image data
            full_image_data = self.read_image_as_base64(relative_path)
            if not full_image_data:
                return None

            # Get thumbnail data
            thumbnail_path = relative_path.replace(
                "profile_pictures/", "profile_pictures/thumbnails/"
            )
            thumbnail_data = self.read_image_as_base64(thumbnail_path)

            return {
                "full_image": full_image_data,
                "thumbnail": thumbnail_data
            }

        except Exception as e:
            logger.error(f"Failed to get profile picture data for {relative_path}: {str(e)}")
            return None

    def _compress_image(self, image_path: Path, output_path: Path) -> bool:
        """
        Compress an image file

        Args:
            image_path: Path to the original image
            output_path: Path where compressed image will be saved

        Returns:
            True if compression was successful, False otherwise
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary (for JPEG compatibility)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Resize if image is too large
                if max(img.size) > settings.MAX_IMAGE_DIMENSION:
                    img.thumbnail((settings.MAX_IMAGE_DIMENSION, settings.MAX_IMAGE_DIMENSION), Image.Resampling.LANCZOS)

                # Apply auto-orientation based on EXIF data
                img = ImageOps.exif_transpose(img)

                # Save with compression
                img.save(
                    output_path,
                    format='JPEG',
                    quality=settings.COMPRESSION_QUALITY,
                    optimize=True
                )

                logger.info(f"Image compressed: {image_path} -> {output_path}")
                return True

        except Exception as e:
            logger.error(f"Failed to compress image {image_path}: {str(e)}")
            return False

    def _compress_document(self, file_path: Path, output_path: Path) -> bool:
        """
        Compress a document file using gzip

        Args:
            file_path: Path to the original file
            output_path: Path where compressed file will be saved

        Returns:
            True if compression was successful, False otherwise
        """
        try:
            with open(file_path, 'rb') as f_in:
                with gzip.open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)

            logger.info(f"Document compressed: {file_path} -> {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to compress document {file_path}: {str(e)}")
            return False

    def _should_compress(self, file_path: Path, file_size: int) -> bool:
        """
        Determine if a file should be compressed

        Args:
            file_path: Path to the file
            file_size: Size of the file in bytes

        Returns:
            True if file should be compressed, False otherwise
        """
        if not settings.ENABLE_COMPRESSION:
            return False

        if file_size < settings.COMPRESSION_THRESHOLD:
            return False

        # Check if file is an image
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'}
        if file_path.suffix.lower() in image_extensions:
            return True

        # Check if file is a document
        document_extensions = {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'}
        if file_path.suffix.lower() in document_extensions:
            return True

        return False

    def compress_file(self, file_path: str) -> Optional[str]:
        """
        Compress a file and return the path to the compressed version

        Args:
            file_path: Relative path to the file to compress

        Returns:
            Relative path to compressed file, or None if compression failed
        """
        try:
            original_path = self.upload_dir / file_path
            if not original_path.exists():
                logger.error(f"File not found for compression: {original_path}")
                return None

            file_size = original_path.stat().st_size

            if not self._should_compress(original_path, file_size):
                logger.info(f"File does not need compression: {file_path}")
                return file_path  # Return original path

            # Create compressed file path
            compressed_filename = f"{original_path.stem}_compressed{original_path.suffix}"
            compressed_path = self.compressed_dir / compressed_filename

            # Ensure compressed directory exists
            compressed_path.parent.mkdir(exist_ok=True)

            # Compress based on file type
            success = False
            if original_path.suffix.lower() in {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'}:
                success = self._compress_image(original_path, compressed_path)
            else:
                # For documents, add .gz extension
                compressed_path = compressed_path.with_suffix(compressed_path.suffix + '.gz')
                success = self._compress_document(original_path, compressed_path)

            if success:
                # Calculate compression ratio
                compressed_size = compressed_path.stat().st_size
                compression_ratio = (1 - compressed_size / file_size) * 100

                logger.info(f"Compression successful: {file_path} -> {compressed_path.relative_to(self.upload_dir)} "
                           f"(Reduced by {compression_ratio:.1f}%)")

                return str(compressed_path.relative_to(self.upload_dir))
            else:
                return None

        except Exception as e:
            logger.error(f"Failed to compress file {file_path}: {str(e)}")
            return None

    def decompress_file(self, compressed_file_path: str, output_path: str = None) -> Optional[str]:
        """
        Decompress a compressed file

        Args:
            compressed_file_path: Path to the compressed file
            output_path: Optional output path, if not provided, will use original name

        Returns:
            Path to decompressed file, or None if decompression failed
        """
        try:
            compressed_path = self.upload_dir / compressed_file_path
            if not compressed_path.exists():
                logger.error(f"Compressed file not found: {compressed_path}")
                return None

            # Determine output path
            if output_path:
                output_file_path = self.upload_dir / output_path
            else:
                # Remove _compressed suffix and .gz extension if present
                if compressed_path.name.endswith('.gz'):
                    base_name = compressed_path.stem
                    if base_name.endswith('_compressed'):
                        base_name = base_name[:-11]  # Remove '_compressed'
                    output_file_path = compressed_path.parent / base_name
                else:
                    # For image files, just remove _compressed suffix
                    base_name = compressed_path.stem
                    if base_name.endswith('_compressed'):
                        base_name = base_name[:-11]
                    output_file_path = compressed_path.parent / f"{base_name}{compressed_path.suffix}"

            # Ensure output directory exists
            output_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Decompress based on file type
            if compressed_path.suffix == '.gz':
                # Decompress gzipped file
                with gzip.open(compressed_path, 'rb') as f_in:
                    with open(output_file_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                # For images, just copy (they're already in usable format)
                shutil.copy2(compressed_path, output_file_path)

            logger.info(f"File decompressed: {compressed_file_path} -> {output_file_path.relative_to(self.upload_dir)}")
            return str(output_file_path.relative_to(self.upload_dir))

        except Exception as e:
            logger.error(f"Failed to decompress file {compressed_file_path}: {str(e)}")
            return None

    def get_file_info(self, file_path: str) -> Dict:
        """
        Get comprehensive information about a file including compression status

        Args:
            file_path: Relative path to the file

        Returns:
            Dictionary with file information
        """
        try:
            full_path = self.upload_dir / file_path
            if not full_path.exists():
                return {"exists": False, "error": "File not found"}

            stat = full_path.stat()

            # Check if there's a compressed version
            compressed_versions = []

            # Check for compressed image version
            if full_path.suffix.lower() in {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'}:
                compressed_name = f"{full_path.stem}_compressed{full_path.suffix}"
                compressed_path = self.compressed_dir / compressed_name
                if compressed_path.exists():
                    compressed_stat = compressed_path.stat()
                    compression_ratio = (1 - compressed_stat.st_size / stat.st_size) * 100
                    compressed_versions.append({
                        "path": str(compressed_path.relative_to(self.upload_dir)),
                        "size": compressed_stat.st_size,
                        "compression_ratio": f"{compression_ratio:.1f}%"
                    })

            # Check for gzipped version
            compressed_gz_name = f"{full_path.stem}_compressed{full_path.suffix}.gz"
            compressed_gz_path = self.compressed_dir / compressed_gz_name
            if compressed_gz_path.exists():
                compressed_stat = compressed_gz_path.stat()
                compression_ratio = (1 - compressed_stat.st_size / stat.st_size) * 100
                compressed_versions.append({
                    "path": str(compressed_gz_path.relative_to(self.upload_dir)),
                    "size": compressed_stat.st_size,
                    "compression_ratio": f"{compression_ratio:.1f}%"
                })

            return {
                "path": str(full_path),
                "relative_path": file_path,
                "size": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "created": datetime.fromtimestamp(stat.st_mtime),  # Using mtime instead of deprecated ctime
                "modified": datetime.fromtimestamp(stat.st_mtime),
                "exists": True,
                "compressed_versions": compressed_versions,
                "has_compression": len(compressed_versions) > 0
            }

        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {str(e)}")
            return {"exists": False, "error": str(e)}

# Global instance
file_storage = FileStorageService()
