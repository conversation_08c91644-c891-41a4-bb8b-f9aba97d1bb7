# File Compression and Large File Support

## Overview

The EduFair platform now supports larger file uploads (up to 20MB) with automatic compression to optimize storage space and improve performance.

## Key Features

### 📈 **Increased File Size Limits**
- **Maximum file size**: 20MB (increased from 10MB)
- **Supported file types**: Images, documents, and videos
- **Automatic validation**: Files are validated before upload

### 🗜️ **Automatic Compression**
- **Smart compression**: Files larger than 1MB are automatically compressed
- **Image compression**: JPEG optimization with 85% quality, max 1920px dimension
- **Document compression**: Gzip compression for text files and documents
- **Lossless for videos**: Video files are stored as-is (compression planned for future)

### 🎯 **Compression Configuration**
```python
# Configuration settings
MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB
COMPRESSION_THRESHOLD = 1 * 1024 * 1024  # 1MB
COMPRESSION_QUALITY = 85  # Image quality (1-100)
MAX_IMAGE_DIMENSION = 1920  # Max width/height for images
```

## File Type Support

### 🖼️ **Images**
- **Formats**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.bmp`, `.tiff`
- **Compression**: Automatic JPEG conversion with quality optimization
- **Resizing**: Large images resized to max 1920px while maintaining aspect ratio
- **EXIF handling**: Auto-rotation based on EXIF orientation data

### 📄 **Documents**
- **Formats**: `.pdf`, `.doc`, `.docx`, `.txt`, `.rtf`, `.odt`
- **Compression**: Gzip compression for text-based files
- **Preservation**: Binary formats like PDF are compressed without data loss

### 🎥 **Videos** (New)
- **Formats**: `.mp4`, `.avi`, `.mov`, `.wmv`, `.flv`, `.webm`
- **Storage**: Currently stored without compression
- **Future**: Video compression planned for future releases

## API Endpoints

### 📤 **Upload Endpoints** (Enhanced)
All existing upload endpoints now support larger files and automatic compression:

- `POST /api/files/profile-picture` - Profile picture upload
- `POST /api/files/task-attachment` - Task attachment upload
- `POST /api/files/student-task-attachment` - Student submission upload

### 🔧 **Compression Management** (New)

#### Compress File
```http
POST /api/files/compress/{file_path}
```
Manually compress an existing file.

**Response:**
```json
{
  "message": "File compressed successfully",
  "original_file": {
    "path": "task_attachments/documents/file.pdf",
    "size": 5242880,
    "size_mb": 5.0
  },
  "compressed_file": {
    "path": "compressed/file_compressed.pdf.gz",
    "size": 1048576,
    "size_mb": 1.0
  },
  "compression_ratio": "80.0%",
  "space_saved_mb": 4.0
}
```

#### Decompress File
```http
POST /api/files/decompress/{compressed_file_path}
```
Decompress a compressed file.

#### File Information
```http
GET /api/files/info/{file_path}
```
Get detailed file information including compression status.

**Response:**
```json
{
  "message": "File information retrieved successfully",
  "file_info": {
    "path": "task_attachments/images/photo.jpg",
    "relative_path": "task_attachments/images/photo.jpg",
    "size": 2097152,
    "size_mb": 2.0,
    "created": "2025-08-14T12:00:00",
    "modified": "2025-08-14T12:00:00",
    "exists": true,
    "compressed_versions": [
      {
        "path": "compressed/photo_compressed.jpg",
        "size": 524288,
        "compression_ratio": "75.0%"
      }
    ],
    "has_compression": true
  }
}
```

## Storage Structure

```
uploads/
├── profile_pictures/
│   ├── thumbnails/
│   └── [user_profile_images]
├── task_attachments/
│   ├── images/
│   └── documents/
└── compressed/          # New compressed files directory
    ├── [compressed_images]
    └── [compressed_documents.gz]
```

## Benefits

### 💾 **Storage Optimization**
- **Space savings**: 50-80% reduction in storage usage for large files
- **Cost efficiency**: Reduced storage costs for cloud deployments
- **Performance**: Faster file transfers and reduced bandwidth usage

### 🚀 **Performance Improvements**
- **Faster uploads**: Optimized file processing during upload
- **Reduced bandwidth**: Smaller files mean faster downloads
- **Better UX**: Quicker file operations for users

### 🔒 **Maintained Quality**
- **Smart compression**: Only compress when beneficial
- **Quality preservation**: Configurable compression levels
- **Lossless options**: Gzip for documents maintains perfect fidelity

## Implementation Details

### 🔄 **Automatic Processing**
1. **Upload validation**: File size and type checking
2. **Storage**: Save original file to appropriate directory
3. **Compression check**: Determine if compression is beneficial
4. **Compression**: Apply appropriate compression method
5. **Logging**: Track compression ratios and space savings

### 🛡️ **Error Handling**
- **Graceful degradation**: If compression fails, original file is preserved
- **Validation**: Comprehensive file validation before processing
- **Logging**: Detailed error logging for troubleshooting

### 📊 **Monitoring**
- **Compression ratios**: Track space savings across all files
- **Performance metrics**: Monitor compression processing time
- **Storage statistics**: Track total storage usage and savings

## Migration Notes

### ✅ **Backward Compatibility**
- **Existing files**: All existing files continue to work without changes
- **API compatibility**: All existing API endpoints maintain same interface
- **Database**: No database schema changes required

### 🔄 **Gradual Rollout**
- **Automatic**: New uploads automatically benefit from compression
- **Manual**: Existing files can be compressed using new API endpoints
- **Optional**: Compression can be disabled via configuration if needed

## Configuration Options

```python
# File size limits
MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB

# Compression settings
ENABLE_COMPRESSION = True
COMPRESSION_THRESHOLD = 1 * 1024 * 1024  # 1MB
COMPRESSION_QUALITY = 85  # Image quality (1-100)
MAX_IMAGE_DIMENSION = 1920  # Max width/height

# File type support
ALLOWED_IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".tiff"}
ALLOWED_DOCUMENT_EXTENSIONS = {".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt"}
ALLOWED_VIDEO_EXTENSIONS = {".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm"}
```

## Next Steps

1. **Restart FastAPI server** to apply all changes
2. **Test file uploads** with larger files (up to 20MB)
3. **Monitor compression ratios** in application logs
4. **Use new API endpoints** for manual file management
5. **Update frontend** to handle larger file uploads if needed

---

**Status**: ✅ **Fully Implemented and Ready for Use**  
**Last Updated**: 2025-08-14
