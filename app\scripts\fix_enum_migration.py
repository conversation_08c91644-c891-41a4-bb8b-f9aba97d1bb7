"""
Complete Enum Migration Fix Script

This script completely resolves enum type conflicts in PostgreSQL database
by detecting and removing all conflicting enum types before allowing
clean migration.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, create_engine
from sqlalchemy.exc import ProgrammingError, IntegrityError
from config.session import get_db
from config.config import settings


def get_all_enum_types(db):
    """Get all enum types in the database"""
    try:
        result = db.execute(text("""
            SELECT 
                n.nspname as schema_name,
                t.typname as enum_name,
                t.oid as type_oid
            FROM pg_type t 
            JOIN pg_namespace n ON t.typnamespace = n.oid
            WHERE t.typtype = 'e' 
            ORDER BY n.nspname, t.typname;
        """))
        return result.fetchall()
    except Exception as e:
        print(f"Error getting enum types: {e}")
        return []


def get_tables_using_enums(db):
    """Get all tables using enum types"""
    try:
        result = db.execute(text("""
            SELECT 
                table_schema,
                table_name, 
                column_name,
                udt_name as enum_type
            FROM information_schema.columns 
            WHERE udt_name LIKE '%enum%'
            ORDER BY table_schema, table_name, column_name;
        """))
        return result.fetchall()
    except Exception as e:
        print(f"Error getting tables using enums: {e}")
        return []


def drop_all_enum_types(db, force=False):
    """Drop all enum types that might conflict"""
    
    # List of known enum types that might conflict
    enum_types = [
        'usertypeenum',
        'eventstatusenum', 
        'registrationstatusenum',
        'taskstatusenum',
        'examstatusenum',
        'questiontypeenum',
        'difficultylevelenum',
        'competitionstatusenum',
        'mentorassignmentstatusenum',
        'mentorverificationstatusenum',
        'associationstatusenum',
        'associationtypeenum'
    ]
    
    dropped_count = 0
    errors = []
    
    for enum_type in enum_types:
        try:
            # Try to drop the enum type
            db.execute(text(f"DROP TYPE IF EXISTS {enum_type} CASCADE;"))
            print(f"   ✅ Dropped enum type: {enum_type}")
            dropped_count += 1
        except Exception as e:
            error_msg = f"Failed to drop {enum_type}: {str(e)}"
            errors.append(error_msg)
            print(f"   ⚠️  {error_msg}")
    
    if force:
        # Get all existing enum types and try to drop them
        existing_enums = get_all_enum_types(db)
        for schema, enum_name, oid in existing_enums:
            if enum_name.endswith('enum'):
                try:
                    if schema != 'public':
                        full_name = f"{schema}.{enum_name}"
                    else:
                        full_name = enum_name
                    
                    db.execute(text(f"DROP TYPE IF EXISTS {full_name} CASCADE;"))
                    print(f"   ✅ Force dropped enum type: {full_name}")
                    dropped_count += 1
                except Exception as e:
                    error_msg = f"Failed to force drop {full_name}: {str(e)}"
                    errors.append(error_msg)
                    print(f"   ⚠️  {error_msg}")
    
    return dropped_count, errors


def check_alembic_state(db):
    """Check and optionally reset alembic state"""
    try:
        # Check if alembic_version table exists
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'alembic_version'
            );
        """))
        
        table_exists = result.scalar()
        
        if table_exists:
            # Get current version
            result = db.execute(text("SELECT version_num FROM alembic_version;"))
            current_version = result.scalar()
            
            print(f"📋 Alembic version table exists")
            if current_version:
                print(f"📋 Current alembic version: {current_version}")
                return current_version
            else:
                print("📋 No alembic version found in table")
                return None
        else:
            print("📋 Alembic version table does not exist")
            return None
    
    except Exception as e:
        print(f"❌ Error checking alembic state: {e}")
        return None


def reset_alembic_state(db):
    """Reset alembic state by deleting version entries"""
    try:
        db.execute(text("DELETE FROM alembic_version;"))
        print("✅ Alembic version table cleared")
        return True
    except Exception as e:
        print(f"❌ Error resetting alembic state: {e}")
        return False


def main():
    """Main function to completely fix enum migration issues"""
    
    print("🚀 Starting complete enum migration fix...")
    print("=" * 60)
    
    db = next(get_db())
    
    try:
        # Step 1: Analyze current state
        print("\n🔍 STEP 1: Analyzing current database state...")
        
        existing_enums = get_all_enum_types(db)
        if existing_enums:
            print(f"📋 Found {len(existing_enums)} existing enum types:")
            for schema, enum_name, oid in existing_enums:
                print(f"   - {schema}.{enum_name} (OID: {oid})")
        else:
            print("✅ No existing enum types found")
        
        tables_using_enums = get_tables_using_enums(db)
        if tables_using_enums:
            print(f"📋 Found {len(tables_using_enums)} columns using enum types:")
            for schema, table, column, enum_type in tables_using_enums:
                print(f"   - {schema}.{table}.{column} uses {enum_type}")
        else:
            print("✅ No tables using enum types found")
        
        # Step 2: Check alembic state
        print("\n🔍 STEP 2: Checking alembic state...")
        current_version = check_alembic_state(db)
        
        # Step 3: Clean up enum types
        print("\n🧹 STEP 3: Cleaning up enum types...")
        
        if existing_enums or tables_using_enums:
            print("⚠️  Found existing enum types or tables using enums")
            print("🗑️  Attempting to drop all conflicting enum types...")
            
            # First try normal cleanup
            dropped_count, errors = drop_all_enum_types(db, force=False)
            
            if errors:
                print(f"⚠️  {len(errors)} errors during normal cleanup, trying force cleanup...")
                dropped_count_force, errors_force = drop_all_enum_types(db, force=True)
                dropped_count += dropped_count_force
                errors.extend(errors_force)
            
            if dropped_count > 0:
                print(f"✅ Successfully dropped {dropped_count} enum types")
                db.commit()
            
            if errors:
                print(f"⚠️  {len(errors)} errors occurred during cleanup:")
                for error in errors:
                    print(f"   - {error}")
        else:
            print("✅ No enum cleanup needed")
        
        # Step 4: Reset alembic if needed
        print("\n🔄 STEP 4: Alembic state management...")
        
        if current_version:
            print("⚠️  Alembic version exists, consider resetting for clean migration")
            print("💡 You can reset alembic state by running: alembic stamp head")
        else:
            print("✅ No alembic version conflicts")
        
        # Step 5: Final verification
        print("\n✅ STEP 5: Final verification...")
        
        final_enums = get_all_enum_types(db)
        if final_enums:
            print(f"⚠️  {len(final_enums)} enum types still exist:")
            for schema, enum_name, oid in final_enums:
                print(f"   - {schema}.{enum_name}")
        else:
            print("✅ All conflicting enum types removed")
        
        print("\n" + "=" * 60)
        print("✅ Enum migration fix completed!")
        
        print("\n📋 Next steps:")
        print("   1. Run: alembic stamp head")
        print("   2. Run: alembic upgrade head")
        print("   3. Start your application")
        
        if final_enums:
            print("\n⚠️  If you still get enum errors:")
            print("   1. Try the nuclear option: DROP DATABASE and CREATE DATABASE")
            print("   2. Or manually drop remaining enums and retry")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Critical error during enum migration fix: {e}")
        print("\n🔧 Manual recovery steps:")
        print("   1. Connect to PostgreSQL as superuser")
        print("   2. Run: DROP DATABASE your_database_name;")
        print("   3. Run: CREATE DATABASE your_database_name;")
        print("   4. Run: alembic upgrade head")
        
        db.rollback()
        return 1
    
    finally:
        db.close()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
