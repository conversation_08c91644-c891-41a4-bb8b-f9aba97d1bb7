# Dummy Payment Service

The Dummy Payment Service is the primary payment system for EduFair, providing a complete payment simulation without requiring external payment gateway integration. This allows you to test and use the events system and payment flows without setting up external payment providers.

## How It Works

The system now exclusively uses the dummy payment service for all payment processing:

- **All payments are processed through the dummy service**
- **No external payment gateway required**
- **Full simulation of payment workflows**

## Features

### ✅ **Simulated Payment Processing**
- Creates dummy payment intents with realistic IDs
- Simulates payment confirmation with success/failure scenarios
- Generates fake transaction IDs and charge information
- Supports refund simulation

### ✅ **Configurable Failure Simulation**
- Enable/disable payment failure simulation
- Adjustable failure rate (0-100%)
- Realistic error messages and failure scenarios

### ✅ **Webhook Simulation**
- Simulates payment webhook events
- Compatible with existing webhook handling code
- Generates dummy event data

### ✅ **Database Integration**
- Full integration with EventPayment and EventRegistration models
- Proper status tracking (pending, completed, failed, refunded)
- Transaction history and audit trail

## API Endpoints

### Testing Endpoints (Admin Only)

#### Enable Failure Simulation
```http
POST /api/payments/test/enable-failures?failure_rate=0.1
Authorization: Bearer <admin_token>
```

#### Disable Failure Simulation
```http
POST /api/payments/test/disable-failures
Authorization: Bearer <admin_token>
```

#### Get Service Information
```http
GET /api/payments/test/service-info
Authorization: Bearer <token>
```

### Payment Configuration
```http
GET /api/payments/config
Authorization: Bearer <token>
```

Response includes `is_dummy_mode: true` when using dummy service.

## Usage Examples

### 1. **Testing Event Registration with Payment**

```python
# Register for an event with a paid ticket
registration_data = {
    "event_id": "event-uuid",
    "ticket_id": "ticket-uuid",
    "quantity": 1
}

# This will automatically use dummy payment if Stripe is not configured
response = requests.post("/api/events/{event_id}/register", json=registration_data)
```

### 2. **Testing Payment Confirmation**

```python
# The dummy service will generate a fake payment intent
payment_intent_id = "pi_dummy_1234567890abcdef"

# Confirm the payment (will always succeed unless failure simulation is enabled)
response = requests.post(f"/api/payments/confirm/{payment_intent_id}")
```

### 3. **Testing Refunds**

```python
# Request a refund for a completed payment
response = requests.post(f"/api/payments/payments/{payment_id}/refund", 
                        json={"refund_reason": "Event cancelled"})
```

## Configuration

### Environment Variables

The system now uses dummy payments exclusively. No payment gateway configuration is required:

```env
# No payment gateway configuration needed
# All payments are handled by the dummy service
```

### Programmatic Configuration

```python
from services.DummyPaymentService import dummy_payment_service

# Enable failure simulation for testing error handling
dummy_payment_service.enable_failure_simulation(failure_rate=0.2)  # 20% failure rate

# Disable failure simulation
dummy_payment_service.disable_failure_simulation()
```

## Testing Scenarios

### 1. **Successful Payment Flow**
1. Register for an event with a paid ticket
2. Payment intent is created automatically
3. Confirm payment (will succeed)
4. Registration status updates to confirmed
5. Payment status updates to completed

### 2. **Failed Payment Flow**
1. Enable failure simulation with admin endpoint
2. Register for an event with a paid ticket
3. Payment intent creation or confirmation may fail
4. Registration remains in pending status
5. Payment status shows as failed

### 3. **Refund Flow**
1. Complete a successful payment
2. Request a refund
3. Payment status updates to refunded
4. Registration status may update based on business logic

## Dummy Data Generated

### Payment Intent IDs
- Format: `pi_dummy_[16-char-hex]`
- Example: `pi_dummy_1a2b3c4d5e6f7890`

### Transaction IDs
- Format: `ch_dummy_[16-char-hex]`
- Example: `ch_dummy_9f8e7d6c5b4a3210`

### Refund IDs
- Format: `re_dummy_[16-char-hex]`
- Example: `re_dummy_abcdef1234567890`

## Benefits

### 🚀 **Fast Development**
- No need to set up Stripe accounts for development
- Instant payment processing simulation
- No external API dependencies

### 🧪 **Comprehensive Testing**
- Test both success and failure scenarios
- Configurable failure rates for stress testing
- Full integration with existing payment flows

### 💰 **Cost-Effective**
- No transaction fees during development
- No need for test credit cards
- Unlimited testing without costs

### 🔒 **Safe Testing**
- No real money involved
- No PCI compliance requirements for development
- Safe to test with any data

## Production Use

The dummy payment service is designed for:

1. **Development and Testing**: Full payment workflow simulation
2. **Educational Platforms**: Where real money transactions aren't needed
3. **Internal Systems**: For organizations that handle payments separately
4. **Prototyping**: Quick setup without payment gateway complexity

The dummy payment service provides a complete payment experience without the complexity of external payment gateways, making it perfect for educational platforms and internal systems.
