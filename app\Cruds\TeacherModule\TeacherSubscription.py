from typing import List, Optional
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception
from Models.users import TeacherProfile, TeacherSubscription
from Schemas.TeacherModule.TeacherSubscription import (
    TeacherSubscriptionCreate,
    TeacherSubscriptionOut,
    TeacherSubscriptionUpdate,
    TeacherSubscriptionList,
)

def create_teacher_subscription(db: Session, subscription: TeacherSubscriptionCreate) -> TeacherSubscriptionOut:
    # Verify the teacher profile exists
    profile = db.query(TeacherProfile).filter(
        TeacherProfile.id == subscription.teacher_profile_id
    ).first()
    
    if not profile:
        raise HTTPException(
            status_code=404,
            detail="Teacher profile not found."
        )
    
    # Check if subscription already exists for this profile
    existing_subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.teacher_profile_id == subscription.teacher_profile_id
    ).first()
    
    if existing_subscription:
        raise HTTPException(
            status_code=400,
            detail="Subscription already exists for this teacher profile."
        )

    # Set default start date if not provided
    start_date = subscription.start_date or datetime.now()
    
    db_subscription = TeacherSubscription(
        teacher_profile_id=subscription.teacher_profile_id,
        plan_id=subscription.plan_id,
        start_date=start_date,
        end_date=subscription.end_date,
        status=subscription.status or "active",
        auto_renew=subscription.auto_renew,
        payment_reference=subscription.payment_reference
    )
    db.add(db_subscription)
    db.commit()
    db.refresh(db_subscription)
    
    return TeacherSubscriptionOut.model_validate(db_subscription)

def get_teacher_subscription_by_id(db: Session, subscription_id: uuid.UUID) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(TeacherSubscription.id == subscription_id).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    return TeacherSubscriptionOut.model_validate(subscription)

def get_teacher_subscription_by_profile_id(db: Session, profile_id: uuid.UUID) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.teacher_profile_id == profile_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    return TeacherSubscriptionOut.model_validate(subscription)

def get_all_teacher_subscriptions(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    status_filter: Optional[str] = None
) -> List[TeacherSubscriptionList]:
    query = db.query(TeacherSubscription)
    
    if status_filter:
        query = query.filter(TeacherSubscription.status == status_filter)
    
    subscriptions = query.offset(skip).limit(limit).all()
    return [TeacherSubscriptionList.model_validate(subscription) for subscription in subscriptions]

def get_active_subscriptions(db: Session) -> List[TeacherSubscriptionList]:
    subscriptions = db.query(TeacherSubscription).filter(
        TeacherSubscription.status == "active"
    ).all()
    return [TeacherSubscriptionList.model_validate(subscription) for subscription in subscriptions]

def get_expired_subscriptions(db: Session) -> List[TeacherSubscriptionList]:
    current_time = datetime.now()
    subscriptions = db.query(TeacherSubscription).filter(
        TeacherSubscription.end_date < current_time,
        TeacherSubscription.status == "active"
    ).all()
    return [TeacherSubscriptionList.model_validate(subscription) for subscription in subscriptions]

def update_teacher_subscription(
    db: Session, 
    subscription_id: uuid.UUID, 
    subscription_update: TeacherSubscriptionUpdate
) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.id == subscription_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    if subscription_update.plan_id is not None:
        subscription.plan_id = subscription_update.plan_id
    if subscription_update.end_date is not None:
        subscription.end_date = subscription_update.end_date
    if subscription_update.status is not None:
        subscription.status = subscription_update.status
    if subscription_update.auto_renew is not None:
        subscription.auto_renew = subscription_update.auto_renew
    if subscription_update.payment_reference is not None:
        subscription.payment_reference = subscription_update.payment_reference
    
    db.commit()
    db.refresh(subscription)
    return TeacherSubscriptionOut.model_validate(subscription)

def update_teacher_subscription_by_profile_id(
    db: Session, 
    profile_id: uuid.UUID, 
    subscription_update: TeacherSubscriptionUpdate
) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.teacher_profile_id == profile_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    if subscription_update.plan_id is not None:
        subscription.plan_id = subscription_update.plan_id
    if subscription_update.end_date is not None:
        subscription.end_date = subscription_update.end_date
    if subscription_update.status is not None:
        subscription.status = subscription_update.status
    if subscription_update.auto_renew is not None:
        subscription.auto_renew = subscription_update.auto_renew
    if subscription_update.payment_reference is not None:
        subscription.payment_reference = subscription_update.payment_reference
    
    db.commit()
    db.refresh(subscription)
    return TeacherSubscriptionOut.model_validate(subscription)

def cancel_subscription(db: Session, subscription_id: uuid.UUID) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.id == subscription_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    subscription.status = "cancelled"
    subscription.auto_renew = False
    
    db.commit()
    db.refresh(subscription)
    return TeacherSubscriptionOut.model_validate(subscription)

def renew_subscription(db: Session, subscription_id: uuid.UUID, new_end_date: datetime) -> TeacherSubscriptionOut:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.id == subscription_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    subscription.end_date = new_end_date
    subscription.status = "active"
    
    db.commit()
    db.refresh(subscription)
    return TeacherSubscriptionOut.model_validate(subscription)

def delete_teacher_subscription(db: Session, subscription_id: uuid.UUID) -> None:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.id == subscription_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    db.delete(subscription)
    db.commit()

def delete_teacher_subscription_by_profile_id(db: Session, profile_id: uuid.UUID) -> None:
    subscription = db.query(TeacherSubscription).filter(
        TeacherSubscription.teacher_profile_id == profile_id
    ).first()
    if not subscription:
        raise HTTPException(status_code=404, detail="Teacher subscription not found")
    
    db.delete(subscription)
    db.commit() 