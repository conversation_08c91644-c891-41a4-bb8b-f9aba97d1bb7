import enum
import datetime
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Table, Boolean, JSON, Text, Numeric, Enum, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
import uuid


class CompetitionStatusEnum(enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    ONGOING = "ongoing"
    CHECKING = "checking"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class MentorAssignmentStatusEnum(enum.Enum):
    ASSIGNED = "assigned"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    COMPLETED = "completed"


class CompetitionQuestionTypeEnum(enum.Enum):
    MCQ = "mcq"
    SHORT_ANSWER = "short_answer"
    LONG_ANSWER = "long_answer"
    CODING = "coding"
    ESSAY = "essay"


class CompetitionDifficultyEnum(enum.Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


# --- Competition Questions ---
class CompetitionQuestion(BaseModel):
    __tablename__ = 'competition_questions'
    
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    question_text = Column(Text, nullable=False)
    question_type = Column(Enum(CompetitionQuestionTypeEnum), nullable=False)
    difficulty = Column(Enum(CompetitionDifficultyEnum), default=CompetitionDifficultyEnum.MEDIUM)
    marks = Column(Integer, nullable=False, default=1)
    
    # Question content
    image_url = Column(String, nullable=True)
    code_snippet = Column(Text, nullable=True)
    expected_answer = Column(Text, nullable=True)
    
    # MCQ specific fields
    options = Column(JSON, nullable=True)  # List of options for MCQ
    correct_option = Column(Integer, nullable=True)  # Index of correct option
    
    # Metadata
    subject = Column(String, nullable=True)
    topic = Column(String, nullable=True)
    tags = Column(JSON, nullable=True)  # List of tags
    
    # AI generation info
    is_ai_generated = Column(Boolean, default=False)
    ai_prompt = Column(Text, nullable=True)
    ai_model_used = Column(String, nullable=True)
    
    # Question order and grouping
    question_order = Column(Integer, nullable=True)
    question_group = Column(String, nullable=True)
    
    # Time allocation
    estimated_time_minutes = Column(Integer, nullable=True)
    
    # Relationships
    competition = relationship("Event", back_populates="competition_questions")
    answers = relationship("CompetitionAnswer", back_populates="question", cascade="all, delete-orphan")


# --- Competition Answers ---
class CompetitionAnswer(BaseModel):
    __tablename__ = 'competition_answers'
    
    question_id = Column(UUID(as_uuid=True), ForeignKey('competition_questions.id'), nullable=False)
    participant_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    
    # Answer content
    answer_text = Column(Text, nullable=True)
    selected_option = Column(Integer, nullable=True)  # For MCQ
    answer_files = Column(JSON, nullable=True)  # List of file URLs
    
    # Submission details
    submitted_at = Column(DateTime, default=datetime.datetime.utcnow)
    time_taken_seconds = Column(Integer, nullable=True)
    
    # Scoring
    mentor_score = Column(Numeric(5, 2), nullable=True)
    ai_score = Column(Numeric(5, 2), nullable=True)
    final_score = Column(Numeric(5, 2), nullable=True)
    
    # Feedback
    mentor_feedback = Column(Text, nullable=True)
    ai_feedback = Column(Text, nullable=True)
    
    # Status
    is_checked_by_mentor = Column(Boolean, default=False)
    is_checked_by_ai = Column(Boolean, default=False)
    checked_at = Column(DateTime, nullable=True)
    checked_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Relationships
    question = relationship("CompetitionQuestion", back_populates="answers")
    participant = relationship("User", foreign_keys=[participant_id])
    competition = relationship("Event", foreign_keys=[competition_id], overlaps="competition_answers")
    checker = relationship("User", foreign_keys=[checked_by])


# --- Competition Mentor Assignment ---
class CompetitionMentorAssignment(BaseModel):
    __tablename__ = 'competition_mentor_assignments'
    
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    mentor_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    assigned_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    
    # Assignment details
    status = Column(Enum(MentorAssignmentStatusEnum), default=MentorAssignmentStatusEnum.ASSIGNED)
    assigned_at = Column(DateTime, default=datetime.datetime.utcnow)
    accepted_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Assignment scope
    questions_assigned = Column(JSON, nullable=True)  # List of question IDs
    participants_assigned = Column(JSON, nullable=True)  # List of participant IDs
    
    # Workload and compensation
    estimated_hours = Column(Numeric(5, 2), nullable=True)
    hourly_rate = Column(Numeric(10, 2), nullable=True)
    total_compensation = Column(Numeric(10, 2), nullable=True)
    
    # Progress tracking
    questions_checked = Column(Integer, default=0)
    total_questions = Column(Integer, default=0)
    progress_percentage = Column(Numeric(5, 2), default=0.0)
    
    # Notes and instructions
    assignment_notes = Column(Text, nullable=True)
    special_instructions = Column(Text, nullable=True)
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('competition_id', 'mentor_id', name='unique_competition_mentor'),
    )
    
    # Relationships
    competition = relationship("Event", foreign_keys=[competition_id], overlaps="mentor_assignments")
    mentor = relationship("User", foreign_keys=[mentor_id], overlaps="competition_assignments")
    assigner = relationship("User", foreign_keys=[assigned_by])


# --- Competition Participant Session ---
class CompetitionSession(BaseModel):
    __tablename__ = 'competition_sessions'
    
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    participant_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    
    # Session details
    session_token = Column(String, nullable=False, unique=True)
    started_at = Column(DateTime, default=datetime.datetime.utcnow)
    ended_at = Column(DateTime, nullable=True)
    
    # Security and monitoring
    ip_address = Column(String, nullable=True)
    user_agent = Column(Text, nullable=True)
    browser_fingerprint = Column(Text, nullable=True)
    
    # Session state
    is_active = Column(Boolean, default=True)
    is_submitted = Column(Boolean, default=False)
    is_flagged = Column(Boolean, default=False)
    
    # Time tracking
    total_time_seconds = Column(Integer, default=0)
    remaining_time_seconds = Column(Integer, nullable=True)
    
    # Security events
    tab_switches = Column(Integer, default=0)
    window_blur_events = Column(Integer, default=0)
    copy_paste_events = Column(Integer, default=0)
    right_click_events = Column(Integer, default=0)
    
    # Proctoring data
    webcam_enabled = Column(Boolean, default=False)
    screen_recording_enabled = Column(Boolean, default=False)
    microphone_enabled = Column(Boolean, default=False)
    
    # Violation tracking
    violations = Column(JSON, nullable=True)  # List of violation events
    violation_count = Column(Integer, default=0)
    
    # Final submission
    submitted_at = Column(DateTime, nullable=True)
    submission_ip = Column(String, nullable=True)
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('competition_id', 'participant_id', name='unique_competition_participant_session'),
    )
    
    # Relationships
    competition = relationship("Event", foreign_keys=[competition_id], overlaps="competition_sessions")
    participant = relationship("User", foreign_keys=[participant_id])


# --- Competition Results ---
class CompetitionResult(BaseModel):
    __tablename__ = 'competition_results'
    
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    participant_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    
    # Scoring
    total_score = Column(Numeric(8, 2), nullable=False, default=0.0)
    max_possible_score = Column(Numeric(8, 2), nullable=False)
    percentage_score = Column(Numeric(5, 2), nullable=False, default=0.0)
    
    # Ranking
    rank = Column(Integer, nullable=True)
    percentile = Column(Numeric(5, 2), nullable=True)
    
    # Time performance
    total_time_taken = Column(Integer, nullable=True)  # in seconds
    time_efficiency = Column(Numeric(5, 2), nullable=True)  # percentage
    
    # Question-wise performance
    questions_attempted = Column(Integer, default=0)
    questions_correct = Column(Integer, default=0)
    questions_incorrect = Column(Integer, default=0)
    questions_unanswered = Column(Integer, default=0)
    
    # Category-wise scores
    category_scores = Column(JSON, nullable=True)  # Scores by subject/topic
    
    # Status
    is_final = Column(Boolean, default=False)
    is_published = Column(Boolean, default=False)
    published_at = Column(DateTime, nullable=True)
    
    # Certificates and awards
    certificate_url = Column(String, nullable=True)
    awards = Column(JSON, nullable=True)  # List of awards/achievements
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('competition_id', 'participant_id', name='unique_competition_participant_result'),
    )
    
    # Relationships
    competition = relationship("Event", foreign_keys=[competition_id], overlaps="competition_results")
    participant = relationship("User", foreign_keys=[participant_id])
