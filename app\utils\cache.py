"""
Redis Caching Utilities for Performance Optimization
"""

import json
import pickle
import hashlib
from typing import Any, Optional, Callable, Union
from functools import wraps
from datetime import timed<PERSON>ta
import redis.asyncio as redis
from config.redis import get_redis


class CacheManager:
    """Centralized cache management for EduFair"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            cached_data = await self.redis.get(key)
            if cached_data:
                return pickle.loads(cached_data)
            return None
        except Exception as e:
            print(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """Set value in cache with expiration"""
        try:
            serialized_data = pickle.dumps(value)
            await self.redis.setex(key, expire, serialized_data)
            return True
        except Exception as e:
            print(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            print(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            print(f"Cache exists error for key {key}: {e}")
            return False
    
    def generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        # Create a unique key based on function arguments
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"cache:{prefix}:{key_hash}"


# Cache decorators for different use cases
def cache_result(expire: int = 3600, key_prefix: str = None):
    """
    Decorator to cache function results
    
    Args:
        expire: Cache expiration time in seconds (default: 1 hour)
        key_prefix: Custom prefix for cache key (default: function name)
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get Redis client
            redis_client = await get_redis()
            cache_manager = CacheManager(redis_client)
            
            # Generate cache key
            prefix = key_prefix or func.__name__
            cache_key = cache_manager.generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache first
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function if not in cache
            if hasattr(func, '__await__'):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Store result in cache
            await cache_manager.set(cache_key, result, expire)
            
            return result
        return wrapper
    return decorator


# Specific cache configurations for different data types
class CacheConfig:
    """Cache configuration constants"""
    
    # Student statistics caching (30 minutes)
    STUDENT_STATS_EXPIRE = 1800
    
    # Rankings caching (15 minutes - more frequent updates)
    RANKINGS_EXPIRE = 900
    
    # Subject/Chapter lists (2 hours - rarely change)
    METADATA_EXPIRE = 7200
    
    # AI checking results (permanent - never changes once set)
    AI_RESULTS_EXPIRE = 86400 * 7  # 1 week
    
    # User profiles (1 hour)
    USER_PROFILE_EXPIRE = 3600
    
    # Exam data (30 minutes)
    EXAM_DATA_EXPIRE = 1800


# Specific caching functions for common operations
async def cache_student_statistics(student_id: str, statistics_data: dict, redis_client: redis.Redis):
    """Cache student statistics with appropriate expiration"""
    cache_manager = CacheManager(redis_client)
    key = f"student_stats:{student_id}"
    await cache_manager.set(key, statistics_data, CacheConfig.STUDENT_STATS_EXPIRE)


async def get_cached_student_statistics(student_id: str, redis_client: redis.Redis) -> Optional[dict]:
    """Get cached student statistics"""
    cache_manager = CacheManager(redis_client)
    key = f"student_stats:{student_id}"
    return await cache_manager.get(key)


async def cache_student_ranking(student_id: str, ranking_data: dict, redis_client: redis.Redis):
    """Cache student ranking with appropriate expiration"""
    cache_manager = CacheManager(redis_client)
    key = f"student_ranking:{student_id}"
    await cache_manager.set(key, ranking_data, CacheConfig.RANKINGS_EXPIRE)


async def get_cached_student_ranking(student_id: str, redis_client: redis.Redis) -> Optional[dict]:
    """Get cached student ranking"""
    cache_manager = CacheManager(redis_client)
    key = f"student_ranking:{student_id}"
    return await cache_manager.get(key)


async def cache_subject_list(subjects_data: list, redis_client: redis.Redis):
    """Cache subject list (rarely changes)"""
    cache_manager = CacheManager(redis_client)
    key = "subjects_list"
    await cache_manager.set(key, subjects_data, CacheConfig.METADATA_EXPIRE)


async def get_cached_subject_list(redis_client: redis.Redis) -> Optional[list]:
    """Get cached subject list"""
    cache_manager = CacheManager(redis_client)
    key = "subjects_list"
    return await cache_manager.get(key)


async def cache_ai_checking_result(exam_id: str, student_id: str, result_data: dict, redis_client: redis.Redis):
    """Cache AI checking results (permanent)"""
    cache_manager = CacheManager(redis_client)
    key = f"ai_result:{exam_id}:{student_id}"
    await cache_manager.set(key, result_data, CacheConfig.AI_RESULTS_EXPIRE)


async def get_cached_ai_checking_result(exam_id: str, student_id: str, redis_client: redis.Redis) -> Optional[dict]:
    """Get cached AI checking results"""
    cache_manager = CacheManager(redis_client)
    key = f"ai_result:{exam_id}:{student_id}"
    return await cache_manager.get(key)


async def invalidate_student_cache(student_id: str, redis_client: redis.Redis):
    """Invalidate all cache entries for a student (when data changes)"""
    cache_manager = CacheManager(redis_client)
    
    # List of cache keys to invalidate
    keys_to_delete = [
        f"student_stats:{student_id}",
        f"student_ranking:{student_id}",
        # Add more student-specific cache keys as needed
    ]
    
    for key in keys_to_delete:
        await cache_manager.delete(key)


async def invalidate_rankings_cache(redis_client: redis.Redis):
    """Invalidate all ranking caches (when new exam results are added)"""
    cache_manager = CacheManager(redis_client)
    
    # This would require a pattern-based deletion
    # For now, we'll implement a simple approach
    # In production, consider using Redis SCAN with pattern matching
    
    # Note: This is a simplified implementation
    # In production, you might want to maintain a set of active ranking cache keys
    pass


# Cache warming functions (pre-populate cache with frequently accessed data)
async def warm_cache_for_active_students(redis_client: redis.Redis, db_session):
    """Pre-populate cache for active students"""
    # This would be called during off-peak hours or after major data updates
    # Implementation would fetch and cache data for recently active students
    pass


# Cache monitoring and statistics
async def get_cache_stats(redis_client: redis.Redis) -> dict:
    """Get cache performance statistics"""
    try:
        info = await redis_client.info('memory')
        return {
            'used_memory': info.get('used_memory_human', 'N/A'),
            'used_memory_peak': info.get('used_memory_peak_human', 'N/A'),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'hit_rate': info.get('keyspace_hits', 0) / max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1) * 100
        }
    except Exception as e:
        return {'error': str(e)}


# Usage examples:
"""
# Using the decorator
@cache_result(expire=1800, key_prefix="student_performance")
async def get_student_performance(student_id: str):
    # Expensive database operation
    return calculate_performance(student_id)

# Manual caching
redis_client = await get_redis()
await cache_student_statistics(student_id, stats_data, redis_client)
cached_stats = await get_cached_student_statistics(student_id, redis_client)
"""
