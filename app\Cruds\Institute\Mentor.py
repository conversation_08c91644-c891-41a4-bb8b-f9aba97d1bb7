from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import bcrypt
import json

# Import Models
from Models.users import User, UserTypeEnum, MentorProfile, MentorInstituteAssociation, InstituteProfile
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Institute.Mentor import (
    MentorRegistrationBase, MentorProfileUpdate, MentorVerificationUpdate,
    MentorUserOut, MentorDetailedOut, MentorProfileOut, MentorListOut, MentorListResponse,
    MentorSearchFilter, MentorStatsOut, MentorApplicationCreate,
    InstituteInvitationCreate, AssociationResponseCreate,
    MentorInstituteAssociationOut, MentorInstituteAssociationDetailedOut,
    AssociationSearchFilter
)
from Schemas.Institute.Institute import InstituteListOut

# Import Utilities
from utils.image_utils import get_profile_image_data


def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def register_mentor(db: Session, mentor_data: MentorRegistrationBase) -> MentorUserOut:
    """Register a new mentor"""
    
    # Check if username already exists
    existing_user = db.query(User).filter(User.username == mentor_data.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    # Check if email already exists
    existing_email = db.query(User).filter(User.email == mentor_data.email).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email already exists")
    
    # Check if mobile already exists
    existing_mobile = db.query(User).filter(User.mobile == mentor_data.mobile).first()
    if existing_mobile:
        raise HTTPException(status_code=400, detail="Mobile number already exists")
    
    # Create user account
    hashed_password = hash_password(mentor_data.password)
    
    user = User(
        username=mentor_data.username,
        email=mentor_data.email,
        mobile=mentor_data.mobile,
        password_hash=hashed_password,
        country=mentor_data.country,
        user_type=UserTypeEnum.mentor,
        is_email_verified=False,
        is_mobile_verified=False
    )
    
    db.add(user)
    db.flush()  # Get the user ID
    
    # Create mentor profile
    mentor_profile = MentorProfile(
        user_id=user.id,
        bio=mentor_data.bio,
        experience_years=mentor_data.experience_years,
        languages=json.dumps(mentor_data.languages) if mentor_data.languages else None,
        hourly_rate=mentor_data.hourly_rate,
        availability_hours=json.dumps(mentor_data.availability_hours) if mentor_data.availability_hours else None
    )

    db.add(mentor_profile)
    db.flush()  # Get the mentor profile ID

    # Add expertise subjects
    if mentor_data.expertise_subject_ids:
        from Models.users import Subject
        expertise_subjects = db.query(Subject).filter(Subject.id.in_(mentor_data.expertise_subject_ids)).all()
        mentor_profile.expertise_subjects.extend(expertise_subjects)

    # Add preferred subjects
    if mentor_data.preferred_subject_ids:
        from Models.users import Subject
        preferred_subjects = db.query(Subject).filter(Subject.id.in_(mentor_data.preferred_subject_ids)).all()
        mentor_profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return MentorUserOut.model_validate(user)


def get_mentor_by_id(db: Session, mentor_id: uuid.UUID) -> MentorDetailedOut:
    """Get mentor by ID with detailed information"""

    try:
        user = db.query(User).options(
            joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
            joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
        ).filter(
            User.id == mentor_id,
            User.user_type == UserTypeEnum.mentor
        ).first()

        if not user:
            raise HTTPException(status_code=404, detail="Mentor not found")

        if not user.mentor_profile:
            # Create a basic mentor profile if it doesn't exist
            mentor_profile = MentorProfile(
                user_id=user.id,
                bio=None,
                experience_years=None,
                languages=json.dumps([]),  # Empty list as JSON string
                hourly_rate=None,
                availability_hours=json.dumps({})  # Empty dict as JSON string
            )
            db.add(mentor_profile)
            db.commit()
            db.refresh(mentor_profile)
            user.mentor_profile = mentor_profile

        # Get statistics
        total_competitions = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).count()

        active_institutes = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.mentor_id == mentor_id,
            MentorInstituteAssociation.status == "active"
        ).count()

        # Convert mentor profile to schema with proper data transformation
        mentor_profile = user.mentor_profile

        # Parse JSON fields safely
        try:
            languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
        except (json.JSONDecodeError, TypeError):
            languages = []

        try:
            availability_hours = json.loads(mentor_profile.availability_hours) if mentor_profile.availability_hours else {}
        except (json.JSONDecodeError, TypeError):
            availability_hours = {}

        # Convert subject relationships to dictionaries
        expertise_subjects = []
        if hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            for subject in mentor_profile.expertise_subjects:
                if subject:  # Additional safety check
                    expertise_subjects.append({
                        "id": str(subject.id),
                        "name": subject.name
                    })

        preferred_subjects = []
        if hasattr(mentor_profile, 'preferred_subjects') and mentor_profile.preferred_subjects:
            for subject in mentor_profile.preferred_subjects:
                if subject:  # Additional safety check
                    preferred_subjects.append({
                        "id": str(subject.id),
                        "name": subject.name
                    })

        # Get profile image data - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)

        # Use user.profile_picture as the primary source, fallback to mentor_profile.profile_image_url
        primary_image_url = user_profile_picture or mentor_profile_image_url
        profile_image_data = get_profile_image_data(primary_image_url, None)

        # Sync mentor_profile.profile_image_url with user.profile_picture if they're different
        if user_profile_picture and mentor_profile_image_url != user_profile_picture:
            mentor_profile.profile_image_url = user_profile_picture
            db.commit()

        # Create profile data with properly formatted fields
        profile_data = MentorProfileOut(
            id=mentor_profile.id,
            user_id=mentor_profile.user_id,
            bio=mentor_profile.bio,
            experience_years=mentor_profile.experience_years,
            languages=languages,
            hourly_rate=mentor_profile.hourly_rate,
            availability_hours=availability_hours,
            profile_image_url=primary_image_url,  # Use the primary image URL (user.profile_picture)
            profile_image=profile_image_data,
            expertise_subjects=expertise_subjects,
            preferred_subjects=preferred_subjects,
            created_at=mentor_profile.created_at,
            updated_at=mentor_profile.updated_at
        )

        # Get user profile image data (same as profile image data for mentors)
        user_profile_image_data = profile_image_data

        # Create user data without the mentor_profile to avoid circular reference
        user_data = MentorUserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            mobile=user.mobile,
            country=user.country or "",
            profile_picture=user.profile_picture,
            profile_image=user_profile_image_data,
            user_type=user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type),
            is_email_verified=user.is_email_verified,
            is_mobile_verified=user.is_mobile_verified,
            created_at=user.created_at,
            mentor_profile=profile_data
        )

        return MentorDetailedOut(
            user=user_data,
            profile=profile_data,
            total_competitions=total_competitions,
            active_institutes=active_institutes,
            average_rating=None,  # Will be calculated separately if needed
            verification_status="pending"  # Default status, will be updated if verification system exists
        )

    except Exception as e:
        print(f"Error in get_mentor_by_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving mentor profile: {str(e)}")


def get_mentors(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    search: Optional[str] = None
) -> MentorListResponse:
    """Get mentors with filtering and pagination"""
    query = db.query(User).join(MentorProfile, MentorProfile.user_id == User.id).filter(User.user_type == UserTypeEnum.mentor)

    # Add search functionality
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                MentorProfile.bio.ilike(search_term)
            )
        )

    return query.offset(skip).limit(limit).all()
   
def update_mentor_profile(
    db: Session,
    mentor_id: uuid.UUID,
    profile_update: MentorProfileUpdate
) -> MentorDetailedOut:
    """Update mentor profile"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle subject relationships separately
    expertise_subject_ids = update_data.pop('expertise_subject_ids', None)
    preferred_subject_ids = update_data.pop('preferred_subject_ids', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            # Handle JSON fields
            if field in ['languages'] and value:
                setattr(profile, field, json.dumps(value))
            elif field == 'availability_hours' and value:
                setattr(profile, field, json.dumps(value))
            else:
                setattr(profile, field, value)

    # Update expertise subjects
    if expertise_subject_ids is not None:
        from Models.users import Subject
        profile.expertise_subjects.clear()
        if expertise_subject_ids:
            expertise_subjects = db.query(Subject).filter(Subject.id.in_(expertise_subject_ids)).all()
            profile.expertise_subjects.extend(expertise_subjects)

    # Update preferred subjects
    if preferred_subject_ids is not None:
        from Models.users import Subject
        profile.preferred_subjects.clear()
        if preferred_subject_ids:
            preferred_subjects = db.query(Subject).filter(Subject.id.in_(preferred_subject_ids)).all()
            profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return get_mentor_by_id(db, mentor_id)


def verify_mentor(
    db: Session,
    mentor_id: uuid.UUID,
    verification_update: MentorVerificationUpdate,
    admin_id: uuid.UUID
) -> MentorDetailedOut:
    """Verify or reject mentor (admin only)"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update verification status
    profile.verification_status = verification_update.verification_status.value
    profile.verification_notes = verification_update.verification_notes
    profile.verified_by = admin_id
    
    if verification_update.verification_status.value == "approved":
        profile.is_verified = True
        profile.verified_at = datetime.now(timezone.utc)
    else:
        profile.is_verified = False
        profile.verified_at = None
    
    db.commit()
    db.refresh(user)
    
    return get_mentor_by_id(db, mentor_id)


# Mentor-Institute Association Functions
def apply_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    application: MentorApplicationCreate
) -> MentorInstituteAssociationOut:
    """Mentor applies to join an institute"""

    # Verify mentor exists and is verified
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    if not mentor.mentor_profile.is_verified:
        raise HTTPException(status_code=400, detail="Mentor must be verified to apply to institutes")

    # Verify institute exists and is verified
    institute = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == application.institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if not institute.institute_profile.is_verified:
        raise HTTPException(status_code=400, detail="Institute must be verified to accept applications")

    # Check if association already exists
    existing_association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.institute_id == application.institute_id
    ).first()

    if existing_association:
        raise HTTPException(status_code=400, detail="Association already exists")

    # Create association
    association = MentorInstituteAssociation(
        mentor_id=mentor_id,
        institute_id=application.institute_id,
        status="pending",
        association_type="mentor_applied",
        application_message=application.application_message,
        hourly_rate=application.proposed_hourly_rate,
        applied_at=datetime.now(timezone.utc)
    )

    db.add(association)
    db.commit()
    db.refresh(association)

    return MentorInstituteAssociationOut.model_validate(association)


def invite_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invitation: InstituteInvitationCreate
) -> MentorInstituteAssociationOut:
    """Institute invites a mentor to join"""

    # Verify institute exists and is verified
    institute = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if not institute.institute_profile.is_verified:
        raise HTTPException(status_code=400, detail="Institute must be verified to invite mentors")

    # Verify mentor exists and is verified
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == invitation.mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    if not mentor.mentor_profile.is_verified:
        raise HTTPException(status_code=400, detail="Mentor must be verified to receive invitations")

    # Check if association already exists
    existing_association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == invitation.mentor_id,
        MentorInstituteAssociation.institute_id == institute_id
    ).first()

    if existing_association:
        raise HTTPException(status_code=400, detail="Association already exists")

    # Create association
    association = MentorInstituteAssociation(
        mentor_id=invitation.mentor_id,
        institute_id=institute_id,
        status="pending",
        association_type="institute_invited",
        application_message=invitation.invitation_message,
        hourly_rate=invitation.offered_hourly_rate,
        contract_terms=invitation.special_terms,
        applied_at=datetime.now(timezone.utc)
    )

    db.add(association)
    db.commit()
    db.refresh(association)

    return MentorInstituteAssociationOut.model_validate(association)


def respond_to_association(
    db: Session,
    association_id: uuid.UUID,
    response: AssociationResponseCreate,
    responder_id: uuid.UUID
) -> MentorInstituteAssociationOut:
    """Respond to mentor application or institute invitation"""

    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == association_id
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Association not found")

    # Verify responder has permission
    if association.association_type == "mentor_applied":
        # Institute should respond
        if association.institute_id != responder_id:
            raise HTTPException(status_code=403, detail="Only the institute can respond to this application")
    else:  # institute_invited
        # Mentor should respond
        if association.mentor_id != responder_id:
            raise HTTPException(status_code=403, detail="Only the mentor can respond to this invitation")

    if association.status != "pending":
        raise HTTPException(status_code=400, detail="Association has already been responded to")

    # Update association
    association.response_message = response.response_message
    association.responded_at = datetime.now(timezone.utc)

    if response.accept:
        association.status = "approved"
        association.approved_at = datetime.now(timezone.utc)
        association.approved_by = responder_id
        association.start_date = datetime.now(timezone.utc)

        if response.negotiated_rate:
            association.hourly_rate = response.negotiated_rate

        if response.contract_terms:
            association.contract_terms = response.contract_terms
    else:
        association.status = "rejected"

    db.commit()
    db.refresh(association)

    return MentorInstituteAssociationOut.model_validate(association)


def get_mentor_associations(
    db: Session,
    mentor_id: uuid.UUID,
    filters: AssociationSearchFilter = None,
    skip: int = 0,
    limit: int = 20
) -> List[MentorInstituteAssociationDetailedOut]:
    """Get mentor's institute associations"""

    query = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor_id
    )

    if filters:
        if filters.status:
            query = query.filter(MentorInstituteAssociation.status == filters.status.value)
        if filters.association_type:
            query = query.filter(MentorInstituteAssociation.association_type == filters.association_type.value)

    associations = query.order_by(desc(MentorInstituteAssociation.applied_at)).offset(skip).limit(limit).all()

    detailed_associations = []
    for assoc in associations:
        # Get mentor and institute details
        mentor = db.query(User).options(joinedload(User.mentor_profile)).filter(User.id == assoc.mentor_id).first()
        institute = db.query(User).options(joinedload(User.institute_profile)).filter(User.id == assoc.institute_id).first()

        # Convert institute to dict to avoid circular import issues
        institute_dict = None
        if institute and institute.institute_profile:
            institute_dict = {
                "id": institute.id,
                "username": institute.username,
                "institute_name": institute.institute_profile.institute_name,
                "city": institute.institute_profile.city,
                "state": institute.institute_profile.state,
                "country": institute.country,
                "institute_type": institute.institute_profile.institute_type,
                "is_verified": institute.institute_profile.is_verified,
                "verification_status": institute.institute_profile.verification_status,
                "logo_url": institute.institute_profile.logo_url,
                "created_at": institute.created_at
            }

        detailed_associations.append(MentorInstituteAssociationDetailedOut(
            association=MentorInstituteAssociationOut.model_validate(assoc),
            mentor=MentorListOut.model_validate(mentor) if mentor else None,
            institute=institute_dict
        ))

    return detailed_associations


def get_institute_associations(
    db: Session,
    institute_id: uuid.UUID,
    filters: AssociationSearchFilter = None,
    skip: int = 0,
    limit: int = 20
) -> List[MentorInstituteAssociationDetailedOut]:
    """Get institute's mentor associations"""

    query = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id
    )

    if filters:
        if filters.status:
            query = query.filter(MentorInstituteAssociation.status == filters.status.value)
        if filters.association_type:
            query = query.filter(MentorInstituteAssociation.association_type == filters.association_type.value)

    associations = query.order_by(desc(MentorInstituteAssociation.applied_at)).offset(skip).limit(limit).all()

    detailed_associations = []
    for assoc in associations:
        # Get mentor and institute details
        mentor = db.query(User).options(joinedload(User.mentor_profile)).filter(User.id == assoc.mentor_id).first()
        institute = db.query(User).options(joinedload(User.institute_profile)).filter(User.id == assoc.institute_id).first()

        detailed_associations.append(MentorInstituteAssociationDetailedOut(
            association=MentorInstituteAssociationOut.model_validate(assoc),
            mentor=MentorListOut.model_validate(mentor) if mentor else None,
            institute=InstituteListOut.model_validate(institute) if institute else None
        ))

    return detailed_associations
