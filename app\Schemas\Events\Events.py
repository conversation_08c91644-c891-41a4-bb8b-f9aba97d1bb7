from pydantic import BaseModel, <PERSON>, validator
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class EventCategoryEnum(str, Enum):
    WORKSHOP = "workshop"
    CONFERENCE = "conference"
    WEBINAR = "webinar"
    COMPETITION = "competition"


class EventStatusEnum(str, Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


class TicketStatusEnum(str, Enum):
    ACTIVE = "active"
    SOLD_OUT = "sold_out"
    INACTIVE = "inactive"


class RegistrationStatusEnum(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    ATTENDED = "attended"


class PaymentStatusEnum(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"


# Base Schemas
class EventCategoryBase(BaseModel):
    name: str = Field(..., max_length=100, description="Category name")
    description: Optional[str] = Field(None, description="Category description")
    color: Optional[str] = Field(None, max_length=7, description="Hex color code")
    icon: Optional[str] = Field(None, max_length=50, description="Icon name or class")
    is_active: bool = Field(True, description="Whether category is active")


class EventLocationBase(BaseModel):
    name: str = Field(..., max_length=200, description="Location name")
    address: Optional[str] = Field(None, description="Full address")
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    latitude: Optional[Decimal] = Field(None, description="Latitude coordinate")
    longitude: Optional[Decimal] = Field(None, description="Longitude coordinate")
    capacity: Optional[int] = Field(None, description="Maximum capacity")
    facilities: Optional[List[str]] = Field(None, description="Available facilities")
    contact_info: Optional[Dict[str, Any]] = Field(None, description="Contact details")
    is_virtual: bool = Field(False, description="Whether location is virtual")
    virtual_link: Optional[str] = Field(None, max_length=500, description="Virtual meeting link")


class EventSpeakerBase(BaseModel):
    name: str = Field(..., max_length=200, description="Speaker name")
    title: Optional[str] = Field(None, max_length=200, description="Professional title")
    bio: Optional[str] = Field(None, description="Speaker biography")
    profile_image_url: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    twitter_url: Optional[str] = Field(None, max_length=500)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    expertise_areas: Optional[List[str]] = Field(None, description="Areas of expertise")
    is_featured: bool = Field(False, description="Whether speaker is featured")


class EventTicketBase(BaseModel):
    name: str = Field(..., max_length=200, description="Ticket type name")
    description: Optional[str] = Field(None, description="Ticket description")
    price: Decimal = Field(..., ge=0, description="Ticket price")
    currency: str = Field("USD", max_length=3, description="Currency code")
    total_quantity: Optional[int] = Field(None, ge=0, description="Total available tickets")
    status: TicketStatusEnum = Field(TicketStatusEnum.ACTIVE)
    sale_start: Optional[datetime] = Field(None, description="Sale start time")
    sale_end: Optional[datetime] = Field(None, description="Sale end time")
    min_quantity_per_order: int = Field(1, ge=1)
    max_quantity_per_order: Optional[int] = Field(None, ge=1)
    requires_approval: bool = Field(False)
    is_transferable: bool = Field(True)
    is_refundable: bool = Field(False)
    refund_policy: Optional[str] = Field(None)
    terms_and_conditions: Optional[str] = Field(None)


class EventBase(BaseModel):
    title: str = Field(..., max_length=300, description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = Field(None, description="Gallery image URLs")
    start_datetime: datetime = Field(..., description="Event start time")
    end_datetime: datetime = Field(..., description="Event end time")
    registration_start: Optional[datetime] = Field(None)
    registration_end: Optional[datetime] = Field(None)
    category_id: UUID = Field(..., description="Event category ID")
    location_id: Optional[UUID] = Field(None, description="Event location ID")
    status: EventStatusEnum = Field(EventStatusEnum.DRAFT)
    is_featured: bool = Field(False)
    is_public: bool = Field(True)
    requires_approval: bool = Field(False)
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = Field(None, description="Event agenda")
    requirements: Optional[str] = Field(None, description="Event requirements")
    tags: Optional[List[str]] = Field(None, description="Event tags")
    external_links: Optional[Dict[str, str]] = Field(None, description="External links")
    is_competition: bool = Field(False)
    competition_exam_id: Optional[UUID] = Field(None)
    competition_rules: Optional[str] = Field(None)
    prize_details: Optional[Dict[str, Any]] = Field(None)

    @validator('end_datetime')
    def validate_end_after_start(cls, v, values):
        if 'start_datetime' in values and v <= values['start_datetime']:
            raise ValueError('End datetime must be after start datetime')
        return v

    @validator('registration_end')
    def validate_registration_end(cls, v, values):
        if v and 'start_datetime' in values and v > values['start_datetime']:
            raise ValueError('Registration end must be before event start')
        return v


# Create Schemas
class EventCategoryCreate(EventCategoryBase):
    pass


class EventLocationCreate(EventLocationBase):
    pass


class EventSpeakerCreate(EventSpeakerBase):
    pass


class EventTicketCreate(EventTicketBase):
    event_id: UUID = Field(..., description="Event ID")


class EventCreate(EventBase):
    organizer_id: Optional[UUID] = Field(None, description="Organizer ID (auto-filled from current user)")


# Update Schemas
class EventCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    color: Optional[str] = Field(None, max_length=7)
    icon: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None


class EventLocationUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    latitude: Optional[Decimal] = None
    longitude: Optional[Decimal] = None
    capacity: Optional[int] = None
    facilities: Optional[List[str]] = None
    contact_info: Optional[Dict[str, Any]] = None
    is_virtual: Optional[bool] = None
    virtual_link: Optional[str] = Field(None, max_length=500)


class EventSpeakerUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    title: Optional[str] = Field(None, max_length=200)
    bio: Optional[str] = None
    profile_image_url: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    twitter_url: Optional[str] = Field(None, max_length=500)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    expertise_areas: Optional[List[str]] = None
    is_featured: Optional[bool] = None


class EventTicketUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0)
    currency: Optional[str] = Field(None, max_length=3)
    total_quantity: Optional[int] = Field(None, ge=0)
    status: Optional[TicketStatusEnum] = None
    sale_start: Optional[datetime] = None
    sale_end: Optional[datetime] = None
    min_quantity_per_order: Optional[int] = Field(None, ge=1)
    max_quantity_per_order: Optional[int] = Field(None, ge=1)
    requires_approval: Optional[bool] = None
    is_transferable: Optional[bool] = None
    is_refundable: Optional[bool] = None
    refund_policy: Optional[str] = None
    terms_and_conditions: Optional[str] = None


class EventUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = None
    start_datetime: Optional[datetime] = None
    end_datetime: Optional[datetime] = None
    registration_start: Optional[datetime] = None
    registration_end: Optional[datetime] = None
    category_id: Optional[UUID] = None
    location_id: Optional[UUID] = None
    status: Optional[EventStatusEnum] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    requires_approval: Optional[bool] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = None
    requirements: Optional[str] = None
    tags: Optional[List[str]] = None
    external_links: Optional[Dict[str, str]] = None
    is_competition: Optional[bool] = None
    competition_exam_id: Optional[UUID] = None
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None


# Output Schemas
class EventCategoryOut(EventCategoryBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventLocationOut(EventLocationBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventSpeakerOut(EventSpeakerBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventTicketOut(EventTicketBase):
    id: UUID
    event_id: UUID
    sold_quantity: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventMinimalOut(BaseModel):
    id: UUID
    title: str
    short_description: Optional[str]
    banner_image_url: Optional[str]
    start_datetime: datetime
    end_datetime: datetime
    status: EventStatusEnum
    is_featured: bool
    category_id: UUID
    location_id: Optional[UUID]

    class Config:
        from_attributes = True


class EventOut(EventBase):
    id: UUID
    organizer_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventDetailedOut(EventOut):
    category: Optional[EventCategoryOut] = None
    location: Optional[EventLocationOut] = None
    speakers: List[EventSpeakerOut] = []
    tickets: List[EventTicketOut] = []
    total_registrations: int = 0
    available_tickets: int = 0

    class Config:
        from_attributes = True


# Registration Schemas
class EventRegistrationBase(BaseModel):
    quantity: int = Field(1, ge=1, description="Number of tickets")
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Additional attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact info")


class EventRegistrationCreate(EventRegistrationBase):
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket type ID")


class EventRegistrationOut(EventRegistrationBase):
    id: UUID
    event_id: UUID
    ticket_id: Optional[UUID]
    user_id: UUID
    registration_number: str
    status: RegistrationStatusEnum
    total_amount: Decimal
    currency: str
    registered_at: datetime
    confirmed_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    attended_at: Optional[datetime]
    payment_status: PaymentStatusEnum
    payment_reference: Optional[str]
    payment_method: Optional[str]

    class Config:
        from_attributes = True


class EventRegistrationDetailedOut(EventRegistrationOut):
    event: EventMinimalOut
    ticket: Optional[EventTicketOut] = None

    class Config:
        from_attributes = True


# Payment Schemas
class EventPaymentCreate(BaseModel):
    registration_id: UUID = Field(..., description="Registration ID")
    payment_method: str = Field(..., description="Payment method")
    amount: Optional[Decimal] = Field(None, description="Payment amount (auto-calculated if not provided)")


class EventPaymentOut(BaseModel):
    id: UUID
    registration_id: UUID
    amount: Decimal
    currency: str
    payment_method: str
    payment_gateway: Optional[str]
    gateway_transaction_id: Optional[str]
    status: PaymentStatusEnum
    processed_at: Optional[datetime]
    failed_at: Optional[datetime]
    refunded_at: Optional[datetime]
    failure_reason: Optional[str]
    refund_reason: Optional[str]
    refund_amount: Optional[Decimal]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# List and Filter Schemas
class EventListFilter(BaseModel):
    category_id: Optional[UUID] = None
    location_id: Optional[UUID] = None
    status: Optional[EventStatusEnum] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    is_competition: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search: Optional[str] = None
    tags: Optional[List[str]] = None


class EventListResponse(BaseModel):
    events: List[EventMinimalOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# Calendar Schemas
class CalendarEventOut(BaseModel):
    id: UUID
    title: str
    start_datetime: datetime
    end_datetime: datetime
    category: EventCategoryOut
    location: Optional[EventLocationOut]
    is_registered: bool = False
    registration_status: Optional[RegistrationStatusEnum] = None

    class Config:
        from_attributes = True


class CalendarResponse(BaseModel):
    events: List[CalendarEventOut]
    month: int
    year: int
