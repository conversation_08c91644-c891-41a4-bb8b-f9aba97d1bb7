# Mentor Profile Picture Sync Fix

## Issue Description

The mentor profile picture was not syncing between the `/me` endpoint and the `/profile` endpoint:

- **`/me` endpoint**: Correctly returned profile picture from `user.profile_picture`
- **`/profile` endpoint**: Was trying to use `mentor_profile.profile_image_url` which was often null or outdated

This caused inconsistency where the same user would see different profile pictures on different endpoints.

## Root Cause

The issue was in the mentor profile retrieval logic in `app/Cruds/Institute/Mentor.py`:

```python
# ❌ BEFORE: Was prioritizing mentor_profile.profile_image_url
profile_image_url = getattr(mentor_profile, 'profile_image_url', None)
profile_image_data = get_profile_image_data(user.profile_picture, profile_image_url)
```

The problem was that when users uploaded profile pictures, they were stored in `user.profile_picture`, but the mentor profile endpoint was looking for `mentor_profile.profile_image_url` first.

## Solution Implemented

### 1. **Updated Profile Retrieval Logic**

**File**: `app/Cruds/Institute/Mentor.py`

```python
# ✅ AFTER: Now prioritizes user.profile_picture
user_profile_picture = user.profile_picture
mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)

# Use user.profile_picture as the primary source, fallback to mentor_profile.profile_image_url
primary_image_url = user_profile_picture or mentor_profile_image_url
profile_image_data = get_profile_image_data(primary_image_url, None)

# Sync mentor_profile.profile_image_url with user.profile_picture if they're different
if user_profile_picture and mentor_profile_image_url != user_profile_picture:
    mentor_profile.profile_image_url = user_profile_picture
    db.commit()
```

### 2. **Enhanced Profile Picture Upload**

**File**: `app/Routes/file_upload.py`

Added sync logic to both profile picture upload endpoints:

```python
# Update user's profile picture in database
current_user.profile_picture = file_path

# If user is a mentor, also update mentor_profile.profile_image_url for consistency
if hasattr(current_user, 'user_type') and current_user.user_type.value == "mentor":
    # Load mentor profile if not already loaded
    if not hasattr(current_user, 'mentor_profile') or current_user.mentor_profile is None:
        from Models.users import MentorProfile
        mentor_profile = db.query(MentorProfile).filter(MentorProfile.user_id == current_user.id).first()
        if mentor_profile:
            mentor_profile.profile_image_url = file_path
    elif current_user.mentor_profile:
        current_user.mentor_profile.profile_image_url = file_path
```

### 3. **Auto-Sync on Profile Access**

The mentor profile now automatically syncs the profile image URLs when accessed, ensuring consistency even for existing data.

## Files Modified

1. **`app/Cruds/Institute/Mentor.py`**
   - Updated `get_mentor_by_id()` function
   - Added prioritization logic for profile images
   - Added auto-sync functionality

2. **`app/Routes/file_upload.py`**
   - Enhanced both profile picture upload endpoints
   - Added mentor profile sync logic
   - Added proper relationship loading

## Benefits

### ✅ **Consistency**
- `/me` and `/profile` endpoints now return the same profile picture
- No more confusion about which image is displayed

### ✅ **Automatic Sync**
- Profile picture uploads now update both storage locations
- Existing data is automatically synced when accessed

### ✅ **Backward Compatibility**
- Existing profile pictures continue to work
- No data migration required
- Graceful fallback for edge cases

### ✅ **Future-Proof**
- Handles both `user.profile_picture` and `mentor_profile.profile_image_url`
- Maintains consistency going forward

## Data Flow

### Before Fix:
```
Profile Upload → user.profile_picture ✅
/me endpoint → user.profile_picture ✅
/profile endpoint → mentor_profile.profile_image_url ❌ (often null)
```

### After Fix:
```
Profile Upload → user.profile_picture ✅ + mentor_profile.profile_image_url ✅
/me endpoint → user.profile_picture ✅
/profile endpoint → user.profile_picture (primary) ✅
Auto-sync → mentor_profile.profile_image_url = user.profile_picture ✅
```

## Testing

To verify the fix:

1. **Upload a profile picture** as a mentor user
2. **Call `/me` endpoint** - should return profile picture data
3. **Call `/profile` endpoint** - should return the same profile picture data
4. **Both endpoints should now be in sync** 🎉

## API Response Consistency

Both endpoints now return consistent profile image data:

```json
{
  "profile_image": {
    "data": "base64_encoded_image_data",
    "content_type": "image/jpeg",
    "filename": "profile.jpg",
    "size": 12345,
    "url": "profile_pictures/user_image.jpg"
  },
  "profile_image_url": "profile_pictures/user_image.jpg"
}
```

## Migration Notes

- **No database migration required**
- **Existing data is automatically synced**
- **No breaking changes to API responses**
- **Backward compatible with existing frontend code**

---

**Status**: ✅ **Fixed and Ready for Testing**  
**Impact**: High - Resolves user-facing inconsistency  
**Risk**: Low - Backward compatible changes only  
**Testing**: Recommended to verify both endpoints return same data
