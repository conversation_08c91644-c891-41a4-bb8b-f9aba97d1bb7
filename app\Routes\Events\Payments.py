from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from uuid import UUID
import json

# Import CRUD functions
from Cruds.Events.Payments import (
    create_payment_for_registration, confirm_payment, get_payment_by_id,
    get_user_payments, get_registration_payments, request_refund,
    process_webhook_event, get_payment_statistics
)

# Import Schemas
from Schemas.Events.Events import EventPaymentOut

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

# Import services
from services import payment_service, dummy_payment_service


def get_payment_service():
    """Get the appropriate payment service (now always dummy)"""
    return payment_service  # This is now the dummy service

router = APIRouter()


@router.post("/registrations/{registration_id}/payment")
def create_payment(
    registration_id: UUID,
    payment_method: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create payment for event registration"""
    current_user = get_current_user(token, db)
    return create_payment_for_registration(db, registration_id, payment_method, current_user.id)


@router.post("/confirm/{payment_intent_id}", response_model=EventPaymentOut)
def confirm_payment_route(
    payment_intent_id: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Confirm payment after successful Stripe payment"""
    current_user = get_current_user(token, db)
    return confirm_payment(db, payment_intent_id, current_user.id)


@router.get("/payments/{payment_id}", response_model=EventPaymentOut)
def get_payment(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get payment by ID"""
    current_user = get_current_user(token, db)
    return get_payment_by_id(db, payment_id, current_user.id)


@router.get("/my-payments", response_model=List[EventPaymentOut])
def get_my_payments(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's payments"""
    current_user = get_current_user(token, db)
    return get_user_payments(db, current_user.id, skip, limit)


@router.get("/registrations/{registration_id}/payments", response_model=List[EventPaymentOut])
def get_registration_payments_route(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get payments for a specific registration"""
    current_user = get_current_user(token, db)
    return get_registration_payments(db, registration_id, current_user.id)


@router.post("/payments/{payment_id}/refund", response_model=EventPaymentOut)
def request_payment_refund(
    payment_id: UUID,
    refund_reason: str = "requested_by_customer",
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Request refund for a payment"""
    current_user = get_current_user(token, db)
    return request_refund(db, payment_id, current_user.id, refund_reason)


@router.get("/statistics")
def get_payment_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get payment statistics for event organizer"""
    current_user = get_current_user(token, db)
    return get_payment_statistics(db, current_user.id)


@router.post("/webhook/payment")
async def payment_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """Handle payment webhook events (dummy service)"""
    payload = await request.body()
    sig_header = request.headers.get('payment-signature', request.headers.get('stripe-signature', ''))

    if not sig_header:
        # For dummy service, we'll create a dummy signature
        sig_header = "dummy_signature"

    # Verify webhook signature
    current_payment_service = get_payment_service()
    webhook_result = current_payment_service.handle_webhook(payload, sig_header)

    if not webhook_result["success"]:
        raise HTTPException(status_code=400, detail=webhook_result["error"])

    # Process the event
    event = webhook_result["event"]
    result = process_webhook_event(db, event)

    return result


@router.get("/config")
def get_payment_config():
    """Get payment configuration for frontend"""
    config = {
        "payment_gateway": "dummy",
        "supported_payment_methods": ["card", "bank_transfer", "cash", "check"],
        "currencies": ["USD", "EUR", "GBP"],
        "default_currency": "USD",
        "is_dummy_mode": True
    }

    return config


# Admin routes for payment management
@router.get("/admin/payments")
def get_all_payments(
    skip: int = 0,
    limit: int = 50,
    status: str = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get all payments (admin only)"""
    from Models.Events import EventPayment, PaymentStatusEnum
    
    query = db.query(EventPayment)
    
    if status:
        try:
            status_enum = PaymentStatusEnum(status)
            query = query.filter(EventPayment.status == status_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status")
    
    payments = query.offset(skip).limit(limit).all()
    return [EventPaymentOut.model_validate(payment) for payment in payments]


@router.post("/admin/payments/{payment_id}/refund")
def admin_refund_payment(
    payment_id: UUID,
    refund_reason: str = "admin_refund",
    refund_amount: float = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Admin refund payment"""
    from decimal import Decimal
    
    refund_amount_decimal = Decimal(str(refund_amount)) if refund_amount else None
    
    current_payment_service = get_payment_service()
    return current_payment_service.refund_event_payment(
        db=db,
        payment_id=payment_id,
        refund_amount=refund_amount_decimal,
        refund_reason=refund_reason
    )


@router.get("/admin/statistics")
def get_admin_payment_statistics(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get overall payment statistics (admin only)"""
    from sqlalchemy import func
    from Models.Events import EventPayment, PaymentStatusEnum
    
    # Overall statistics
    total_stats = db.query(
        func.sum(EventPayment.amount).label("total_revenue"),
        func.count(EventPayment.id).label("total_payments")
    ).filter(EventPayment.status == PaymentStatusEnum.COMPLETED).first()
    
    # Status breakdown
    status_breakdown = db.query(
        EventPayment.status,
        func.count(EventPayment.id).label("count"),
        func.sum(EventPayment.amount).label("amount")
    ).group_by(EventPayment.status).all()
    
    # Payment method breakdown
    method_breakdown = db.query(
        EventPayment.payment_method,
        func.count(EventPayment.id).label("count"),
        func.sum(EventPayment.amount).label("amount")
    ).filter(EventPayment.status == PaymentStatusEnum.COMPLETED).group_by(EventPayment.payment_method).all()
    
    return {
        "total_revenue": float(total_stats.total_revenue or 0),
        "total_payments": total_stats.total_payments or 0,
        "status_breakdown": [
            {
                "status": status.status.value,
                "count": status.count,
                "amount": float(status.amount or 0)
            }
            for status in status_breakdown
        ],
        "method_breakdown": [
            {
                "method": method.payment_method,
                "count": method.count,
                "amount": float(method.amount or 0)
            }
            for method in method_breakdown
        ]
    }


# Testing endpoints for dummy payment service
@router.post("/test/enable-failures")
def enable_payment_failures(
    failure_rate: float = 0.1,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Enable payment failure simulation for testing (admin only)"""
    current_payment_service = get_payment_service()

    if current_payment_service == dummy_payment_service:
        dummy_payment_service.enable_failure_simulation(failure_rate)
        return {
            "message": f"Payment failure simulation enabled with {failure_rate*100}% failure rate",
            "is_dummy_mode": True
        }
    else:
        return {
            "message": "Failure simulation only available in dummy mode",
            "is_dummy_mode": False
        }


@router.post("/test/disable-failures")
def disable_payment_failures(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Disable payment failure simulation (admin only)"""
    current_payment_service = get_payment_service()

    if current_payment_service == dummy_payment_service:
        dummy_payment_service.disable_failure_simulation()
        return {
            "message": "Payment failure simulation disabled",
            "is_dummy_mode": True
        }
    else:
        return {
            "message": "Failure simulation only available in dummy mode",
            "is_dummy_mode": False
        }


@router.get("/test/service-info")
def get_payment_service_info(
    token: str = Depends(oauth2_scheme)
):
    """Get information about the current payment service"""
    info = {
        "service_type": "dummy",
        "is_dummy_mode": True,
        "failure_simulation_enabled": dummy_payment_service.simulated_failures,
        "failure_rate": dummy_payment_service.failure_rate,
        "description": "Dummy payment service for testing and development"
    }

    return info
